package br.com.alice.member.api

import br.com.alice.common.core.BaseConfig
import br.com.alice.common.core.RunningMode
import com.typesafe.config.ConfigFactory
import io.ktor.server.config.HoconApplicationConfig

object ServiceConfig : BaseConfig(HoconApplicationConfig(ConfigFactory.load("application.conf"))) {

    val signUpUrl get() = config.property("${runningMode.value.lowercase()}.signUpUrl").getString()

    val baseUrl get() = config.property("${runningMode.value.lowercase()}.baseUrl").getString()

    val appDeeplinkUrl get() = config.property("${runningMode.value.lowercase()}.appDeeplinkUrl").getString()

    fun environment(): RunningMode {
        val environmentAsString = config.property("systemEnv").getString()
        return RunningMode.valueOf(environmentAsString.uppercase())
    }

    fun bucket(key: String) = config.property("${runningMode.value.lowercase()}.$key").getString()

    object AppointmentScheduleCalendars {
        val webview get() = config("appointmentScheduleCalendars.webview")
        val immersion get() = config("appointmentScheduleCalendars.immersion")
        val aliceGoogleMapsUrl get() = config("appointmentScheduleCalendars.aliceGoogleMapsUrl")
        val casaAliceAddress get() = config("appointmentScheduleCalendars.casaAliceAddress")
    }

    object FirebaseCredentials {
        val apiKey = config("firebase.apiKey")
        val url = config("firebase.url")
    }

    object Schedule {
        val newAppointmentScheduleWebviewUrl = config("appointmentSchedule.newAppointmentScheduleWebviewUrl")
    }

    object Webhook {
        object ClearSale {
            val token = config("webhook.clearSale.token")
        }
    }

    object AliceAgora {
        val FEATURE_DISCOVERY_CONTENT_URL = config("aliceAgora.featureDiscoveryContentUrl")
    }

    object Labi {
        val baseUrl get() = config("labi.baseUrl")
        val authUrl get() = config("labi.authUrl")
        val username get() = config("labi.username")
        val password get() = config("labi.password")
        val tokenRefreshBuffer get() = config("labi.tokenRefreshBuffer").toLong()
        val cacheExpirationTimeInMinutes get() = config("labi.cache.expirationTimeInMinutes").toLong()

        object Retry {
            val maxRetries get() = config("labi.retry.maxAttempts").toInt()
            val maxDelayMsOnRetry get() = config("labi.retry.maxDelayMs").toLong()
        }

        object Endpoints {
            val getAuthToken get() = config("labi.endpoints.getAuthToken")
            val locationAvailability get() = config("labi.endpoints.locationAvailability")
        }
    }
}
