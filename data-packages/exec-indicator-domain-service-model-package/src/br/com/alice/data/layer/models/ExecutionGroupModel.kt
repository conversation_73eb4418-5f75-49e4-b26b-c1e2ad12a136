package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDateTime
import java.util.UUID
import kotlin.random.Random

data class ExecutionGroupModel(
    override val id: UUID = RangeUUID.generate(),
    val providerUnitId: UUID,
    val code: String = generateRandomCode(),
    val userEmail: String? = null,
    val tags: List<String> = emptyList(),
    val attachments: List<String>? = emptyList(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now()
) : Model {

    companion object {
        fun generateRandomCode(): String {
            return (1..9).map { Random.nextInt(0, 9) }.joinToString("")
        }

    }

}
