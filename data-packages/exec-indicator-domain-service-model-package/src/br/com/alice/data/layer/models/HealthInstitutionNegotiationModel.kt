package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class HealthInstitutionNegotiationModel(
    // Identification fields
    val code: String,
    val description: String,
    var tableType: String,
    val providerUnitGroupId: UUID,
    val healthcareResourceId: UUID,
    val bundlePrimaryTuss: String? = null,

    // Validity fields
    val validAfter: LocalDate,
    val validBefore: LocalDate? = null,
    val activeHealthcareResource: Boolean,

    val externalId: String,

    // Basic fields
    override val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
    var searchTokens: String? = null,
) : Model {
    override fun sanitize() = this.copy(
        description = this.description.lowercase().capitalize(),
    )
}
