package br.com.alice.data.layer.services

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Adder
import br.com.alice.common.service.data.client.CountByValues
import br.com.alice.common.service.data.client.Counter
import br.com.alice.common.service.data.client.Finder
import br.com.alice.common.service.data.client.Getter
import br.com.alice.common.service.data.client.Updater
import br.com.alice.common.service.data.dsl.Field
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.Query
import br.com.alice.common.service.data.dsl.QueryBuilder
import br.com.alice.data.layer.models.ExecIndicatorAuthorizerModel
import br.com.alice.data.layer.services.ExecIndicatorAuthorizerModelDataService.FieldOptions
import br.com.alice.data.layer.services.ExecIndicatorAuthorizerModelDataService.OrderingOptions
import java.util.UUID
import com.github.kittinunf.result.Result

@RemoteService
interface ExecIndicatorAuthorizerModelDataService : Service,
    Finder<FieldOptions, OrderingOptions, ExecIndicatorAuthorizerModel>,
    Counter<FieldOptions, OrderingOptions, ExecIndicatorAuthorizerModel>,
    Adder<ExecIndicatorAuthorizerModel>,
    Getter<ExecIndicatorAuthorizerModel>,
    Updater<ExecIndicatorAuthorizerModel> {

    override val namespace: String
        get() = "exec_indicator"
    override val serviceName: String
        get() = "exec_indicator_authorizer"

    class Id : Field.UUIDField(ExecIndicatorAuthorizerModel::id) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class Domain: Field.TextField(ExecIndicatorAuthorizerModel::domain) {
        fun eq(domain: String) = Predicate.eq(this, domain)
    }
    class UpdatedAt: Field.DateTimeField(ExecIndicatorAuthorizerModel::updatedAt)

    class ProviderUnitId: Field.UUIDField(ExecIndicatorAuthorizerModel::providerUnitId) {
        fun eq(value: UUID) = Predicate.eq(this, value)
        fun inList(value: List<UUID>) = Predicate.inList(this, value)
    }

    class MvCdLocalPrestador: Field.IntegerField(ExecIndicatorAuthorizerModel::mvCdLocalPrestador) {
        fun eq(mvCdLocalPrestador: Int) = Predicate.eq(this, mvCdLocalPrestador)
    }

    class MvCdPrestador: Field.IntegerField(ExecIndicatorAuthorizerModel::mvCdPrestador) {
        fun eq(mvCdPrestador: Int) = Predicate.eq(this, mvCdPrestador)
    }



    class FieldOptions {
        val id = Id()
        val domain = Domain()
        val providerUnitId = ProviderUnitId()
        val mvCdLocalPrestador = MvCdLocalPrestador()
        val mvCdPrestador = MvCdPrestador()
    }

    class OrderingOptions {
        val domain = Domain()
        val updatedAt = UpdatedAt()
    }

    override fun queryBuilder() = QueryBuilder(
        FieldOptions(),
        OrderingOptions()
    )

    override suspend fun findByQuery(query: Query): Result<List<ExecIndicatorAuthorizerModel>, Throwable>

    override suspend fun countByQuery(query: Query): Result<Int, Throwable>
    override suspend fun countGroupedByQuery(query: Query): Result<List<CountByValues>, Throwable>

    override suspend fun existsByQuery(query: Query): Result<Boolean, Throwable>

    override suspend fun get(id: UUID): Result<ExecIndicatorAuthorizerModel, Throwable>

    override suspend fun add(model: ExecIndicatorAuthorizerModel): Result<ExecIndicatorAuthorizerModel, Throwable>

    override suspend fun update(model: ExecIndicatorAuthorizerModel): Result<ExecIndicatorAuthorizerModel, Throwable>

}
