package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID

data class TissGuiaExpense(
    val id: UUID = RangeUUID.generate(),
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),

    val executedAt: LocalDate,
    val executionStartAt: LocalTime? = null,
    val executionEndAt: LocalTime? = null,
    val procedureTable: String,
    val procedureCode: String,
    val quantity: Int,
    val unitMeasure: String? = null,
    val reduceValue: BigDecimal? = null,
    val unityValue: BigDecimal? = null,
    val totalValue: BigDecimal? = null,
    val procedureDescription: String,
    val guiaId: UUID,
    val critique: String? = null,
    val reviewerId: String? = null,
    val expenseCode: String? = null,
    val hash: String? = null,
    val addedOnCriticize: Boolean = false,
    val status: TissGuiaExpenseStatus? = null
)

enum class TissGuiaExpenseStatus {
    AUTHORIZED,
    DENIED,
}
