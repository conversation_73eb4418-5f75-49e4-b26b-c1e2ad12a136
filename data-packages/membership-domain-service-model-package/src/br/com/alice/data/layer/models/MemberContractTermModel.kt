package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.MemberReference
import br.com.alice.common.core.Model
import br.com.alice.common.models.DependentInformation
import br.com.alice.common.serialization.JsonSerializable
import java.time.LocalDateTime
import java.util.UUID

data class MemberContractTermModel(
    override val id: UUID = RangeUUID.generate(),
    override val memberId: UUID,
    val memberContractId: UUID? = null,
    val signature: UserSignatureModel? = null,
    val termType: TermType,
    val signedDocumentFileId: UUID? = null,
    val documentFileId: UUID? = null,
    val version: Int = 0,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, MemberReference, DependentInformation {
    val isSigned get() = signature != null

    fun sign(userSignature: UserSignatureModel, signedDocumentFileId: UUID) =
        copy(signature = userSignature, signedDocumentFileId = signedDocumentFileId)
}

data class UserSignatureModel(
    val id: UUID = RangeUUID.generate(),
    val userAgent: String,
    val ipAddress: String?,
    val signedAt: LocalDateTime = LocalDateTime.now(),
    val idToken: String,
) : JsonSerializable
