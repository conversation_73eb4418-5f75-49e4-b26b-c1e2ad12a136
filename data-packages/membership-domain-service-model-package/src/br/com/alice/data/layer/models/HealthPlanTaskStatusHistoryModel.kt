package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import java.time.LocalDateTime
import java.util.UUID

data class HealthPlanTaskStatusHistoryModel(
    override val id: UUID = RangeUUID.generate(),
    val healthPlanTaskId: UUID,
    override val personId: PersonId,
    val previousStatus: HealthPlanTaskStatus? = null,
    val currentStatus: HealthPlanTaskStatus,
    val version: Int = 0,
    val blameUser: UUID?,
    val blameUserType: HealthPlanTaskStatusHistoryBlameUserType,
    val createdAt: LocalDateTime = LocalDateTime.now(),
    val updatedAt: LocalDateTime = LocalDateTime.now(),
) : Model, PersonReference {}
