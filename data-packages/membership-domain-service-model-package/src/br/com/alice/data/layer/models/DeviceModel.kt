package br.com.alice.data.layer.models

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Model
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.PersonReference
import com.google.gson.annotations.Expose
import java.util.UUID

data class DeviceModel(
    @Expose
    override val personId: PersonId = PersonId(),
    val deviceId: String,
    val appVersion: String? = null,
    val version: Int = 0,
    val voipToken: String? = null,
    override val id: UUID = RangeUUID.generate()
) : Model, PersonReference
