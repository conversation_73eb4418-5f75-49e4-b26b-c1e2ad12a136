package br.com.alice.product.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.common.service.data.client.Getter
import br.com.alice.data.layer.models.CoveredGeoRegion
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface CoveredGeoRegionService : Service,
    Getter<CoveredGeoRegion> {

    override val namespace get() = "product"
    override val serviceName get() = "covered_geo_region"

    override suspend fun get(id: UUID): Result<CoveredGeoRegion, Throwable>

    suspend fun getAll(): Result<List<CoveredGeoRegion>, Throwable>

    suspend fun findByRange(range: IntRange): Result<List<CoveredGeoRegion>, Throwable>

    suspend fun countAll(): Result<Int, Throwable>

    suspend fun search(name: String): Result<List<CoveredGeoRegion>, Throwable>

    suspend fun findByIds(ids: List<UUID>): Result<List<CoveredGeoRegion>, Throwable>

    suspend fun create(coveredGeoRegion: CoveredGeoRegion): Result<CoveredGeoRegion, Throwable>

    suspend fun update(coveredGeoRegion: CoveredGeoRegion): Result<CoveredGeoRegion, Throwable>
}
