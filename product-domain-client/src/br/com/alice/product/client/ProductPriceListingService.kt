package br.com.alice.product.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.PriceListing
import br.com.alice.data.layer.models.ProductPriceListing
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface ProductPriceListingService : Service {

    override val namespace get() = "product"
    override val serviceName get() = "product_price_listing"

    suspend fun findByProductId(productId: UUID): Result<List<ProductPriceListing>, Throwable>
    suspend fun existsByPriceListingId(priceListingId: UUID): Result<Boolean, Throwable>
    suspend fun add(productPriceListing: ProductPriceListing): Result<ProductPriceListing, Throwable>
    suspend fun getCurrentPriceListing(productId: UUID): Result<PriceListing, Throwable>
    suspend fun getCurrentsPriceListings(productIds: List<UUID>): Result<Map<UUID, PriceListing?>, Throwable>
    suspend fun getCurrent(productId: UUID): Result<ProductPriceListing, Throwable>
    suspend fun getCurrents(productIds: List<UUID>): Result<List<ProductPriceListing>, Throwable>
    suspend fun getPriceListing(id: UUID): Result<PriceListing, Throwable>
    suspend fun get(id: UUID): Result<ProductPriceListing, Throwable>
}
