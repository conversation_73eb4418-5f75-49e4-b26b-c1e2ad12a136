package br.com.alice.wanda.consumers

import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.healthlogic.event.HealthLogicRecordCreatedEvent
import br.com.alice.wanda.services.internal.HealthLogicRecordPersonHealthEventService
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test


internal class HealthLogicRecordCreatedConsumerTest: ConsumerTest() {
    private val healthLogicRecordPersonHealthEventService: HealthLogicRecordPersonHealthEventService = mockk()

    private val consumer = HealthLogicRecordCreatedConsumer(healthLogicRecordPersonHealthEventService)

    private val personId = PersonId()
    private val healthLogicRecord = TestModelFactory.buildHealthLogic(personId)
    private val event =  HealthLogicRecordCreatedEvent(healthLogicRecord)
    private val personHealthEvent = mockk<PersonHealthEvent>()

    @Test
    fun `#handleEvent calls service with correct param`() = runBlocking {
        coEvery {
            healthLogicRecordPersonHealthEventService.upsertPersonHealthEvent(healthLogicRecord)
        } returns true.success()

        val result = consumer.handleEvent(event)
        assertThat(result).isSuccessWithData(true)
    }
}
