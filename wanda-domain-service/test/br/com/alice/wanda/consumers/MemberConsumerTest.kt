package br.com.alice.wanda.consumers

import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.verifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.membership.model.events.PersonTaskUpsertedEvent
import br.com.alice.person.model.events.MemberCancelledEvent
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.wanda.logics.PersonTaskPersonHealthEventLogic
import br.com.alice.wanda.services.MembershipService
import br.com.alice.wanda.services.internal.InternalPersonHealthEventService
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class MemberConsumerTest : ConsumerTest() {

    private val membershipService: MembershipService = mockk()
    private val internalPersonHealthEventService: InternalPersonHealthEventService = mockk()
    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val consumer = MemberConsumer(
        membershipService,
        internalPersonHealthEventService,
        healthcareTeamService
    )

    private val personId = PersonId()
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam()
    private val personHealthEvent = TestModelFactory.buildPersonHealthEvent(personId = personId)
    private val date = LocalDateTime.of(2022, 1, 1, 10, 0, 0)

    @BeforeTest
    fun setup() {
        mockkObject(PersonTaskPersonHealthEventLogic)
        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now() } returns date

        coEvery {
            healthcareTeamService.getHealthcareTeamByPerson(personId)
        } returns healthcareTeam.success()

        coEvery {
            internalPersonHealthEventService.upsertPersonHealthEvent(personHealthEvent)
        } returns personHealthEvent.success()
    }

    @AfterTest
    override fun clear() {
        super.clear()
        unmockkStatic(LocalDateTime::class)
    }

    @Test
    fun `#handleMemberCancelled - calls service`() = runBlocking {
        val event = MemberCancelledEvent(member = TestModelFactory.buildMember())
        val healthEvents = listOf(TestModelFactory.buildPersonHealthEvent())
        coEvery { membershipService.cancelPersonHealthEventFromInactiveMember(any()) } returns healthEvents.success()

        val result = consumer.handleMemberCancelled(event)
        assertThat(result).isSuccessWithData(healthEvents)

        coVerifyOnce { membershipService.cancelPersonHealthEventFromInactiveMember(event.payload.member) }
        coVerify { healthcareTeamService wasNot called }
    }

    @Test
    fun `#upsertPersonTaskPersonHealthEvent - new task`() = runBlocking {
        val event = PersonTaskUpsertedEvent(
            task = TestModelFactory.buildPersonTask(personId = personId)
        )

        coEvery {
            internalPersonHealthEventService.findByCategoryPersonIdAndTime(
                personId = event.payload.task.personId,
                category = PersonHealthEventCategory.PERSON_TASK_TEST_REQUEST,
                from = date.minusHours(12)
            )
        } returns null

        every {
            PersonTaskPersonHealthEventLogic.personTaskToPersonHealthEvent(
                event.payload.task,
                healthcareTeam
            )
        } returns personHealthEvent

        val result = consumer.upsertPersonTaskPersonHealthEvent(event)

        assertThat(result).isSuccessWithData(personHealthEvent)

        coVerifyOnce { healthcareTeamService.getHealthcareTeamByPerson(any()) }
        coVerifyOnce { internalPersonHealthEventService.upsertPersonHealthEvent(any()) }
        coVerifyOnce { internalPersonHealthEventService.findByCategoryPersonIdAndTime(any(), any(), any()) }
        verifyOnce { PersonTaskPersonHealthEventLogic.personTaskToPersonHealthEvent(any(), any()) }

        confirmVerified(
            healthcareTeamService,
            PersonTaskPersonHealthEventLogic,
            internalPersonHealthEventService
        )
    }

    @Test
    fun `#upsertPersonTaskPersonHealthEvent - already processed`() = runBlocking {
        val event = PersonTaskUpsertedEvent(
            task = TestModelFactory.buildPersonTask(personId = personId)
        )

        coEvery {
            internalPersonHealthEventService.findByCategoryPersonIdAndTime(
                personId = event.payload.task.personId,
                category = PersonHealthEventCategory.PERSON_TASK_TEST_REQUEST,
                from = date.minusHours(12)
            )
        } returns personHealthEvent

        every {
            PersonTaskPersonHealthEventLogic.unifyPersonTaskInOneHealthEvent(
                event.payload.task,
                personHealthEvent
            )
        } returns personHealthEvent

        val result = consumer.upsertPersonTaskPersonHealthEvent(event)

        assertThat(result).isSuccessWithData(personHealthEvent)

        coVerifyOnce { healthcareTeamService.getHealthcareTeamByPerson(any()) }
        coVerifyOnce { internalPersonHealthEventService.upsertPersonHealthEvent(any()) }
        coVerifyOnce { internalPersonHealthEventService.findByCategoryPersonIdAndTime(any(), any(), any()) }
        verifyOnce { PersonTaskPersonHealthEventLogic.unifyPersonTaskInOneHealthEvent(any(), any()) }

        confirmVerified(
            healthcareTeamService,
            PersonTaskPersonHealthEventLogic,
            internalPersonHealthEventService
        )
    }
}
