package br.com.alice.wanda.logics

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.classSimpleName
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.storage.AliceFile
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Attachment
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.PersonHealthEventCategory.PERSON_TASK_TEST_REQUEST
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.PERSON_TASK
import br.com.alice.data.layer.models.PersonHealthEventStatus.CANCELLED
import br.com.alice.data.layer.models.PersonHealthEventStatus.NOT_STARTED
import br.com.alice.data.layer.models.ReferencedLink
import br.com.alice.wanda.logics.PersonTaskPersonHealthEventLogic.personTaskToPersonHealthEvent
import br.com.alice.wanda.logics.PersonTaskPersonHealthEventLogic.unifyPersonTaskInOneHealthEvent
import br.com.alice.wanda.logics.PersonTaskPersonHealthEventLogic.unifyTestResultFileInOneHealthEvent
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class PersonTaskPersonHealthEventLogicTest {

    private val personId = PersonId()
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam()
    private val personTask = TestModelFactory.buildPersonTask(
        personId = personId,
        attachmentIds = listOf(RangeUUID.generate(), RangeUUID.generate()),
        description = "description"
    )
    private val attachments = listOf(
        Attachment(
            id = personTask.attachmentIds[0],
            fileName = "Pedido de Exame 1",
            type = ""
        ),
        Attachment(
            id = personTask.attachmentIds[1],
            fileName = "Pedido de Exame 2",
            type = ""
        )
    )
    private val staffId = RangeUUID.generate()
//    private val defaultStaffId = FeatureConfig(
//        key = "default_person_task_staff_id",
//        namespace = FeatureNamespace.WANDA,
//        type = FeatureType.STRING,
//        value = staffId.toString(),
//        active = true,
//        description = "",
//    )

    private val personHealthEvent = TestModelFactory.buildPersonHealthEvent(
        personId = personId,
        title = personTask.title,
        category = PERSON_TASK_TEST_REQUEST,
        healthcareTeamId = healthcareTeam.id,
        staffId = staffId,
        status = NOT_STARTED,
        eventDate = personTask.createdAt,
        dueDate = personTask.createdAt.plusDays(3),
        description = personTask.description!!,
        referencedModelClass = PERSON_TASK,
        referencedModelId = personTask.id.toString(),
        attachments = attachments
    )

    @Test
    fun `#personTaskToPersonHealthEvent must create a health person event`() = runBlocking {
        withFeatureFlag(FeatureNamespace.WANDA, "default_person_task_staff_id", staffId.toString()) {

            val result = personTaskToPersonHealthEvent(personTask, healthcareTeam)

            assertThat(result).usingRecursiveComparison().ignoringFields(
                "id",
                "createdAt",
                "updatedAt"
            ).isEqualTo(personHealthEvent)
        }
    }

    @Test
    fun `#personTaskToPersonHealthEvent - without description`() = runBlocking {
        val personTask = personTask.copy(description = null)
        val expected = personHealthEvent.copy(description = "")

        withFeatureFlag(FeatureNamespace.WANDA, "default_person_task_staff_id", staffId.toString()) {
            val result = personTaskToPersonHealthEvent(personTask, healthcareTeam)

            assertThat(result).usingRecursiveComparison().ignoringFields(
                "id",
                "createdAt",
                "updatedAt"
            ).isEqualTo(expected)
        }
    }

    @Test
    fun `#personTaskToPersonHealthEvent - null healthCareTeam`() = runBlocking {
        val expected = personHealthEvent.copy(healthcareTeamId = null, staffId = staffId)

        withFeatureFlag(FeatureNamespace.WANDA, "default_person_task_staff_id", staffId.toString()) {
            val result = personTaskToPersonHealthEvent(personTask, null)

            assertThat(result).usingRecursiveComparison().ignoringFields(
                "id",
                "createdAt",
                "updatedAt"
            ).isEqualTo(expected)
        }
    }

    @Test
    fun `#unifyPersonTaskInOneHealthEvent - add new information on health event`() {
        val newAttachment1 = RangeUUID.generate()
        val newAttachment2 = RangeUUID.generate()
        val personTask = personTask.copy(attachmentIds = listOf(newAttachment1, newAttachment2))

        val attachments = listOf(
            Attachment(
                id = newAttachment1,
                fileName = "Pedido de Exame 1",
                type = ""
            ),
            Attachment(
                id = newAttachment2,
                fileName = "Pedido de Exame 2",
                type = ""
            )
        )
        val expected = personHealthEvent.copy(
            eventDate = personTask.createdAt,
            referencedLinks = personHealthEvent.referencedLinks.plus(
                ReferencedLink(
                    id = personTask.id.toString(),
                    model = personTask.classSimpleName()
                )
            ),
            status = NOT_STARTED,
            attachments = attachments
        )

        val result = unifyPersonTaskInOneHealthEvent(personTask, personHealthEvent)

        assertThat(result).isEqualTo(expected)
    }


    @Test
    fun `#unifyPersonTaskInOneHealthEvent - update health event with cancelled status when attachment list is empty`() {
        val personTask = personTask.copy(attachmentIds = emptyList())

        val expected = personHealthEvent.copy(
            eventDate = personTask.createdAt,
            referencedLinks = personHealthEvent.referencedLinks.plus(
                ReferencedLink(
                    id = personTask.id.toString(),
                    model = personTask.classSimpleName()
                )
            ),
            status = CANCELLED,
            attachments = emptyList()
        )

        val result = unifyPersonTaskInOneHealthEvent(personTask, personHealthEvent)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#unifyPersonTaskInOneHealthEvent - update health event with not stated status when attachment list is same`() {
        val personTask = personTask.copy(attachmentIds = attachments.map { it.id })

        val expected = personHealthEvent.copy(
            eventDate = personTask.createdAt,
            referencedLinks = personHealthEvent.referencedLinks.plus(
                ReferencedLink(
                    id = personTask.id.toString(),
                    model = personTask.classSimpleName()
                )
            ),
            status = NOT_STARTED,
            attachments = attachments
        )

        val result = unifyPersonTaskInOneHealthEvent(personTask, personHealthEvent)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#unifyTestResultFileInOneHealthEvent - should person health event with attachments`() = runBlocking<Unit> {
        val aliceFile = AliceFile(
            id = RangeUUID.generate(),
            fileName = "arquivo",
            type = "pdf",
            url = "www.google.com"
        )
        val testResultFile = TestModelFactory.buildTestResultFile(
            personId = PersonId(),
            file = aliceFile
        )
        val attachments = listOf(
            Attachment(
                id = aliceFile.id,
                fileName = aliceFile.fileName,
                type = aliceFile.type
            )
        )

        val expected = personHealthEvent.copy(
            eventDate = testResultFile.performedAt,
            description = "${personHealthEvent.description} - ${testResultFile.description}",
            attachments = personHealthEvent.attachments.plus(attachments),
        )

        val result = unifyTestResultFileInOneHealthEvent(testResultFile, personHealthEvent)

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }
}
