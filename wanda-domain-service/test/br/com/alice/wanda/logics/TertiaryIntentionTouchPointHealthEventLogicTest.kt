package br.com.alice.wanda.logics

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.PersonHealthEvent
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel
import br.com.alice.data.layer.models.PersonHealthEventStatus
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.TertiaryIntentionTouchPoint
import br.com.alice.data.layer.models.TertiaryIntentionType
import io.mockk.mockkObject
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class TertiaryIntentionTouchPointHealthEventLogicTest {

    private val healthcareTeam: HealthcareTeam = TestModelFactory.buildHealthcareTeam(
        careCoordNurseStaffId = RangeUUID.generate()
    )
    private val personId = PersonId()
    private val eventDate = LocalDateTime.of(2022, 7, 8, 12, 5, 10)

    @BeforeTest
    fun setup() {
        mockkObject(PersonHealthEventLogic)
    }

    @Test
    fun `#buildPersonHealthEvent should convert the tertiary intention and provider unit for PersonHealthEvents`() {
        val providerUnit: ProviderUnit = TestModelFactory.buildProviderUnit(
            name = "Hospital Dona Aranha"
        )
        val tertiaryIntentionTouchPoint = TertiaryIntentionTouchPoint(
            personId = personId,
            startedAt = LocalDateTime.now().minusDays(1),
            providerUnitId = providerUnit.id,
            type = TertiaryIntentionType.TIT_EMERGENCY,
            healthEventId = RangeUUID.generate()
        )

        val expected: PersonHealthEvent = TestModelFactory.buildPersonHealthEvent(
            personId = tertiaryIntentionTouchPoint.personId,
            healthcareAdditionalTeamId = healthcareTeam.id,
            healthcareTeamId = healthcareTeam.id,
            staffId = null,
            title = "Membro passou no Pronto Socorro do Hospital Dona Aranha",
            description = "",
            dueDate = eventDate.plusDays(1),
            eventDate = eventDate,
            referencedModelId = tertiaryIntentionTouchPoint.id.toString(),
            referencedModelClass = PersonHealthEventReferenceModel.TERTIARY_INTENTION_TOUCH_POINT,
            category = PersonHealthEventCategory.SUMMARY_EMERGENCY,
            status = PersonHealthEventStatus.NOT_STARTED
        )

        val result = TertiaryIntentionTouchPointHealthEventLogic.buildPersonHealthEvent(
            tertiaryIntention = tertiaryIntentionTouchPoint,
            providerUnit = providerUnit,
            healthcareTeam = healthcareTeam,
            eventDate = eventDate,
            careCoordGroupId = healthcareTeam.id
        )

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expected)
    }

    @Test
    fun `#buildPersonHealthEvent should convert the tertiary intention and provider unit for PersonHealthEvents to Duquesa member`() {
        val providerUnit: ProviderUnit = TestModelFactory.buildProviderUnit(
            name = "Hospital Dona Aranha"
        )
        val tertiaryIntentionTouchPoint = TertiaryIntentionTouchPoint(
            personId = personId,
            startedAt = LocalDateTime.now().minusDays(1),
            providerUnitId = providerUnit.id,
            type = TertiaryIntentionType.TIT_EMERGENCY,
            healthEventId = RangeUUID.generate()
        )

        val expected: PersonHealthEvent = TestModelFactory.buildPersonHealthEvent(
            personId = tertiaryIntentionTouchPoint.personId,
            healthcareAdditionalTeamId = healthcareTeam.id,
            healthcareTeamId = healthcareTeam.id,
            staffId = null,
            title = "Membro passou no Pronto Socorro do Hospital Dona Aranha",
            description = "",
            dueDate = eventDate.plusDays(1),
            eventDate = eventDate,
            referencedModelId = tertiaryIntentionTouchPoint.id.toString(),
            referencedModelClass = PersonHealthEventReferenceModel.TERTIARY_INTENTION_TOUCH_POINT,
            category = PersonHealthEventCategory.SUMMARY_EMERGENCY,
            status = PersonHealthEventStatus.FINISHED
        )

        val result = TertiaryIntentionTouchPointHealthEventLogic.buildPersonHealthEvent(
            tertiaryIntention = tertiaryIntentionTouchPoint,
            providerUnit = providerUnit,
            healthcareTeam = healthcareTeam,
            eventDate = eventDate,
            careCoordGroupId = healthcareTeam.id,
            status = PersonHealthEventStatus.FINISHED
        )

        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("id", "createdAt", "updatedAt")
            .isEqualTo(expected)
    }

}
