package br.com.alice.wanda.logics

import br.com.alice.channel.notifier.ChannelUpsertedPayload
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ChannelCategory
import br.com.alice.data.layer.models.ChannelChangeAction
import br.com.alice.data.layer.models.ChannelKind
import br.com.alice.data.layer.models.ChannelStatus.ACTIVE
import br.com.alice.data.layer.models.ChannelStatus.ARCHIVED
import br.com.alice.data.layer.models.ChannelSubCategory
import br.com.alice.data.layer.models.ChannelSubCategoryClassifier
import br.com.alice.data.layer.models.ChannelType.ADMINISTRATIVE
import br.com.alice.data.layer.models.ChannelType.ASSISTANCE_CARE
import br.com.alice.data.layer.models.ChannelType.CHAT
import br.com.alice.data.layer.models.ChannelType.HEALTH_PLAN
import br.com.alice.data.layer.models.PersonHealthEventCategory.CHANNEL_ADMINISTRATIVE
import br.com.alice.data.layer.models.PersonHealthEventCategory.CHANNEL_ASSISTANCE_CARE
import br.com.alice.data.layer.models.PersonHealthEventCategory.CHANNEL_CHAT
import br.com.alice.data.layer.models.PersonHealthEventCategory.CHANNEL_HEALTH_PLAN
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel.CHANNEL
import br.com.alice.data.layer.models.PersonHealthEventStatus.FINISHED
import br.com.alice.data.layer.models.PersonHealthEventStatus.IN_PROGRESS
import br.com.alice.wanda.logics.ChannelPersonHealthEventLogic.fromChannelToPersonHealthEvent
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class ChannelPersonHealthEventLogicTest {
    private val personId = PersonId()

    private val channel = ChannelUpsertedPayload(
        name = "Channel",
        channelId = "iWpif9VbDBb20nqK5jAL",
        personId = personId,
        type = ASSISTANCE_CARE,
        kind = ChannelKind.CHANNEL,
        category = ChannelCategory.ASSISTANCE,
        subCategory = ChannelSubCategory.ACUTE,
        status = ACTIVE,
        staffIds = listOf(RangeUUID.generate()),
        action = ChannelChangeAction.UPDATE_STAFF,
        appVersion = "3.0.0"
    )
    private val healthcareTeam = TestModelFactory.buildHealthcareTeam()

    private val expected = TestModelFactory.buildPersonHealthEvent(
        personId = personId,
        staffId = channel.staffIds.first(),
        healthcareTeamId = healthcareTeam.id,
        title = channel.name!!,
        description = "Channel Type: CHANNEL > ASSISTANCE > ACUTE\nTags: ",
        eventDate = channel.createdAt,
        channelId = channel.channelId,
        referencedModelId = channel.channelId,
        referencedModelClass = CHANNEL,
        category = CHANNEL_ASSISTANCE_CARE,
        status = IN_PROGRESS
    )

    @Test
    fun `#fromChannelToPersonHealthEvent - default values`() {
        val result = fromChannelToPersonHealthEvent(channel, healthcareTeam)

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#fromChannelToPersonHealthEvent - empty team`() {
        val expected = expected.copy(healthcareTeamId = null)
        val result = fromChannelToPersonHealthEvent(channel, null)

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#fromChannelToPersonHealthEvent - archived channel`() {
        val channel = channel.copy(status = ARCHIVED)
        val expected = expected.copy(status = FINISHED)

        val result = fromChannelToPersonHealthEvent(channel, healthcareTeam)

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#fromChannelToPersonHealthEvent - ADMINISTRATIVE`() {
        val channel = channel.copy(
            type = ADMINISTRATIVE,
            kind = ChannelKind.CHANNEL,
            category = ChannelCategory.ADMINISTRATIVE,
            subCategory = null
        )
        val expected = expected.copy(
            category = CHANNEL_ADMINISTRATIVE,
            description = "Channel Type: CHANNEL > ADMINISTRATIVE\nTags: "
        )

        val result = fromChannelToPersonHealthEvent(channel, healthcareTeam)

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#fromChannelToPersonHealthEvent - CHAT`() {
        val channel = channel.copy(
            type = CHAT,
            kind = ChannelKind.CHAT,
            category = null,
            subCategory = null
        )
        val expected = expected.copy(
            category = CHANNEL_CHAT,
            description = "Channel Type: CHAT\nTags: "
        )

        val result = fromChannelToPersonHealthEvent(channel, healthcareTeam)

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

    @Test
    fun `#fromChannelToPersonHealthEvent - HEALTH_PLAN`() {
        val channel = channel.copy(
            type = HEALTH_PLAN,
            kind = ChannelKind.CHANNEL,
            category = ChannelCategory.ASSISTANCE,
            subCategory = ChannelSubCategory.LONGITUDINAL,
            subCategoryClassifier = ChannelSubCategoryClassifier.HEALTH_TEAM,
        )
        val expected = expected.copy(
            category = CHANNEL_HEALTH_PLAN,
            description = "Channel Type: CHANNEL > ASSISTANCE > LONGITUDINAL > HEALTH_TEAM\nTags: "
        )

        val result = fromChannelToPersonHealthEvent(channel, healthcareTeam)

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }
}
