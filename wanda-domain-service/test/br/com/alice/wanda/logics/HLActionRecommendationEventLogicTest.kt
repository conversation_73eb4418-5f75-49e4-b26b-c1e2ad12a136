package br.com.alice.wanda.logics

import br.com.alice.common.core.PersonId
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventReferenceModel
import br.com.alice.data.layer.models.PersonHealthEventStatus
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test

class HLActionRecommendationEventLogicTest {

    private val personId = PersonId()
    private val outcome = TestModelFactory.buildClinicalOutcomeRecord(personId = personId)
    private val case = TestModelFactory.buildCaseRecord(personId = personId)
    private val hlRecommendation = TestModelFactory.buildHLActionRecommendations(personId = personId)
    private val team = TestModelFactory.buildHealthcareTeam()


    @Test
    fun `#generateReviewTask adds correct referenced models`() {

        val expected = TestModelFactory.buildPersonHealthEvent(
            personId = personId,
            staffId = case.responsibleStaffId,
            healthcareTeamId = team.id,
            title = "Indicação de Melhora de Condição",
            description = """
                    Olá! De acordo com os valores de referência de desfecho, esse membro indicou melhora da condição 
                    ${case.description.type} ${case.description.value}.
                    Resultado de desfecho: ${outcome.outcome}.
                    Por favor, avalie se ainda há necessidade de mantê-lo atrelado a esta condição.
                """,
            dueDate = outcome.addedAt.plusDays(2).atBeginningOfTheDay(),
            eventDate = outcome.addedAt,
            referencedModelClass = PersonHealthEventReferenceModel.HEALTH_LOGIC_RECOMMENDATION,
            referencedModelId = hlRecommendation.id.toString(),
            category = PersonHealthEventCategory.INTERNAL_TASK,
            status = PersonHealthEventStatus.NOT_STARTED,
        )

        val result = HLActionRecommendationEventLogic.generateReviewTask(
           case, outcome, hlRecommendation, team
        )

        assertThat(result).usingRecursiveComparison().ignoringFields(
            "id",
            "createdAt",
            "updatedAt"
        ).isEqualTo(expected)
    }

}
