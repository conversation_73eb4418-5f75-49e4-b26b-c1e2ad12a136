package br.com.alice.wanda.logics

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.extensions.minLocalDateTimeWithSaoPauloTimeZone
import br.com.alice.common.service.data.dsl.Predicate
import br.com.alice.common.service.data.dsl.and
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.PersonHealthEventCategory
import br.com.alice.data.layer.models.PersonHealthEventCategory.HEALTH_PLAN_TEST_RESULT
import br.com.alice.data.layer.models.PersonHealthEventStatus.FINISHED
import br.com.alice.data.layer.models.PersonHealthEventStatus.IN_PROGRESS
import br.com.alice.data.layer.models.PersonHealthEventStatus.NOT_STARTED
import br.com.alice.data.layer.services.PersonHealthEventDataService.FieldOptions
import br.com.alice.wanda.logics.PredicateLogic.buildPersonHealthEventPredicate
import br.com.alice.wanda.logics.PredicateLogic.buildPoolSummaryCountPredicate
import br.com.alice.wanda.logics.PredicateLogic.buildPoolSummaryDueCountPredicate
import br.com.alice.wanda.logics.PredicateLogic.buildSummaryCountPredicate
import br.com.alice.wanda.logics.PredicateLogic.buildSummaryDueCountPredicate
import io.mockk.clearAllMocks
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkStatic
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import java.time.LocalTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class PredicateLogicTest {
    private val staffIdToSearch = RangeUUID.generate()
    private val healthcareTeamIdToSearch = RangeUUID.generate()

    private val personIdToSearch = TestModelFactory.buildPerson().id

    private val dueDateToSearch = LocalDateTime.parse("2020-01-15T10:00:00")
    private val limitSearchMonths = LocalDateTime.parse("2019-06-15T10:00:00").atBeginningOfTheDay()
    private val categoriesToSearch = listOf(
        HEALTH_PLAN_TEST_RESULT
    )
    private val toSearch = listOf(
        IN_PROGRESS,
        NOT_STARTED
    )

    @BeforeTest
    fun setup() {
        mockkStatic(LocalDateTime::class)
        every { LocalDateTime.now().minusMonths(3) } returns limitSearchMonths
    }

    @AfterTest
    fun clear() {
        unmockkStatic(LocalDateTime::class)
        clearAllMocks()
    }

    @Test
    fun `#buildSummaryCountPredicate - should return expected where condition`() {
        val result = buildSummaryCountPredicate(staffIdToSearch, dueDateToSearch, categoriesToSearch)

        val expected = Predicate.inList(FieldOptions().status, toSearch) and
                    Predicate.eq(FieldOptions().staffId, staffIdToSearch) and
                    Predicate.inList(FieldOptions().category, categoriesToSearch) and
                    Predicate.greater(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MIN)) and
                    Predicate.less(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MAX))

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildSummaryCountPredicate - should ignore category if empty`() {
        val result = buildSummaryCountPredicate(
            staffIdToSearch,
            dueDateToSearch,
            emptyList()
        )

        val expected = Predicate.inList(FieldOptions().status, toSearch) and
                Predicate.eq(FieldOptions().staffId, staffIdToSearch) and
                Predicate.greater(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MIN)) and
                Predicate.less(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MAX))

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildSummaryCountPredicate - should ignore category if a lot`() {
        val result = buildSummaryCountPredicate(
            staffIdToSearch,
            dueDateToSearch,
            PersonHealthEventCategory.values().toList()
        )

        val expected = Predicate.inList(FieldOptions().status, toSearch) and
                Predicate.eq(FieldOptions().staffId, staffIdToSearch) and
                Predicate.greater(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MIN)) and
                Predicate.less(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MAX))

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildSummaryDueCountPredicate - should return expected where condition`() {
        val result = buildSummaryDueCountPredicate(
            staffIdToSearch,
            dueDateToSearch,
            categoriesToSearch
        )

        val expected = Predicate.inList(FieldOptions().status, toSearch) and
                Predicate.eq(FieldOptions().staffId, staffIdToSearch) and
                Predicate.inList(FieldOptions().category, categoriesToSearch) and
                Predicate.less(FieldOptions().dueDate, dueDateToSearch) and
                Predicate.greaterEq(FieldOptions().dueDate, limitSearchMonths)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPoolSummaryCountPredicate - should return expected where condition`() {
        val result = buildPoolSummaryCountPredicate(listOf(staffIdToSearch), dueDateToSearch)

        val expected = Predicate.inList(FieldOptions().healthcareAdditionalTeamId, listOf(staffIdToSearch)) and
                Predicate.inList(FieldOptions().status, toSearch) and
                Predicate.greater(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MIN)) and
                Predicate.less(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MAX))

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPoolSummaryDueCountPredicate - should return expected where condition`() {
        val result = buildPoolSummaryDueCountPredicate(listOf(staffIdToSearch), dueDateToSearch)

        val expected =  Predicate.inList(FieldOptions().healthcareAdditionalTeamId, listOf(staffIdToSearch)) and
                Predicate.inList(FieldOptions().status, toSearch) and
                Predicate.less(FieldOptions().dueDate, dueDateToSearch) and
                Predicate.greaterEq(FieldOptions().dueDate, limitSearchMonths)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildSummaryAutomaticFollowUpCountPredicate - should return expected where condition`() {
        val result = PredicateLogic.buildSummaryAutomaticFollowUpCountPredicate(
            staffIdToSearch,
            dueDateToSearch,
            categoriesToSearch
        )

        val expected = Predicate.inList(FieldOptions().status, toSearch) and
                Predicate.eq(FieldOptions().staffId, staffIdToSearch) and
                Predicate.inList(FieldOptions().category, categoriesToSearch) and
                Predicate.greater(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MIN)) and
                Predicate.less(FieldOptions().dueDate, dueDateToSearch.with(LocalTime.MAX)) and
                Predicate.eq(FieldOptions().automaticFollowUp, true)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPersonHealthEventPredicate - should return expected condition`() {
        val filters = "{\"person_id\": {\"id\": \"${personIdToSearch.id}\"}," +
                "\"category\": [\"HEALTH_PLAN_TEST_RESULT\"]," +
                "\"staff_id\": [\"${staffIdToSearch}\"]," +
                "\"healthcare_team_id\": [\"${healthcareTeamIdToSearch}\"]," +
                "\"health_care_additional_team_id\": [\"${healthcareTeamIdToSearch}\"]," +
                "\"status\": [\"FINISHED\"]," +
                "\"due_date\": {\"first\": \"${dueDateToSearch}\", \"second\": \"${dueDateToSearch.plusDays(1)}\"}}"

        val expected = Predicate.greaterEq(
                    FieldOptions().dueDate,
                    dueDateToSearch.minLocalDateTimeWithSaoPauloTimeZone()
                ) and
                Predicate.inList(FieldOptions().category, listOf(HEALTH_PLAN_TEST_RESULT)) and
                Predicate.less(
                    FieldOptions().dueDate,
                    dueDateToSearch.plusDays(1).minLocalDateTimeWithSaoPauloTimeZone().plusDays(1)
                ) and
                Predicate.eq(FieldOptions().personId, personIdToSearch) and
                Predicate.inList(FieldOptions().healthcareTeamId, listOf(healthcareTeamIdToSearch)) and
                Predicate.inList(FieldOptions().healthcareAdditionalTeamId, listOf(healthcareTeamIdToSearch)) and
                Predicate.inList(FieldOptions().staffId, listOf(staffIdToSearch)) and
                Predicate.inList(FieldOptions().status, listOf(FINISHED))

        val result = buildPersonHealthEventPredicate(
            filters
        )

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPersonHealthEventPredicate - should use default status when filter is empty`() {
        val filterEmpty1 = " "
        val filterEmpty2 = "{ }"
        val filterEmpty3 = "[ ]"

        val result1 = buildPersonHealthEventPredicate(filterEmpty1)
        val result2 = buildPersonHealthEventPredicate(filterEmpty2)
        val result3 = buildPersonHealthEventPredicate(filterEmpty3)

        val expected = Predicate.greaterEq(FieldOptions().dueDate, limitSearchMonths) and
                Predicate.inList(
                    FieldOptions().status,
                    listOf(IN_PROGRESS, NOT_STARTED)
                )

        assertThat(result1).isEqualTo(expected)
        assertThat(result2).isEqualTo(expected)
        assertThat(result3).isEqualTo(expected)
    }

    @Test
    fun `#buildPersonHealthEventPredicate - should build only with since date`() {
        val filters = "{\"due_date\": {\"first\": \"${dueDateToSearch}\"}}"

        val expected =
            Predicate.greaterEq(FieldOptions().dueDate, dueDateToSearch.minLocalDateTimeWithSaoPauloTimeZone())

        val result = buildPersonHealthEventPredicate(
            filters
        )

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#buildPersonHealthEventPredicate - should build only with until date`() {
        val filters = "{\"due_date\": {\"second\": \"${dueDateToSearch.plusDays(1)}\"}}"

        val expected = Predicate.greaterEq(FieldOptions().dueDate, limitSearchMonths) and
                Predicate.less(
                    FieldOptions().dueDate,
                    dueDateToSearch.plusDays(1).minLocalDateTimeWithSaoPauloTimeZone().plusDays(1)
                )

        val result = buildPersonHealthEventPredicate(
            filters
        )

        assertThat(result).isEqualTo(expected)
    }
}
