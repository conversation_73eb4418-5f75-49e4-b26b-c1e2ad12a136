package br.com.alice.sortinghat.consumers

import br.com.alice.clinicalaccount.event.PersonClinicalAccountDeleteEvent
import br.com.alice.clinicalaccount.event.PersonHealthcareTeamAssociationUpdatedEvent
import br.com.alice.sortinghat.services.PersonTeamAssociationServiceImpl

class PersonHealthcareTeamAssociationConsumer(
    private val personTeamAssociationService: PersonTeamAssociationServiceImpl
) : Consumer() {

    suspend fun syncHealthcareTeamAssociation(event: PersonHealthcareTeamAssociationUpdatedEvent) =
        withSubscribersEnvironment {
            val payload = event.payload

            personTeamAssociationService.syncAssociation(
                personId = payload.personId,
                teamId = payload.newHealthcareTeamId,
                quartetId = payload.referenceNursesGroupId
            )
        }

    suspend fun inactiveAssociation(event: PersonClinicalAccountDeleteEvent) = withSubscribersEnvironment {
        val personClinicalAccount = event.payload.personClinicalAccount

        personTeamAssociationService.inactiveAssociation(
            personId = personClinicalAccount.personId,
            teamId = personClinicalAccount.healthcareTeamId
        )
    }
}
