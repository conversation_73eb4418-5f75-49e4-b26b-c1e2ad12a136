package br.com.alice.sortinghat.inputcreators

import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.data.layer.models.RoutingHistoryModel
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.person.client.PersonService
import br.com.alice.sortinghat.models.balances.healthCareTeam.HealthcareTeamBalance
import br.com.alice.sortinghat.models.input.HealthcareTeamModel
import br.com.alice.sortinghat.models.inputs.healthCareTeam.HealthcareTeamInput
import com.github.kittinunf.result.getOrElse
import kotlinx.coroutines.async
import kotlinx.coroutines.coroutineScope

class HealthcareTeamInputCreator(
    private val personService: PersonService,
    private val riskService: RiskService
) : InputCreator<HealthcareTeamInput, HealthcareTeamModel, HealthcareTeamBalance>() {

    override fun modelType() = RoutingHistoryModel.HEALTHCARE_TEAM

    override suspend fun create(
        model: HealthcareTeamModel,
        balance: List<HealthcareTeamBalance>
    ): HealthcareTeamInput = coroutineScope {
        val personTeamAssociationDeferred = async { getPersonTeamAssociation(model.personId).getOrNullIfNotFound() }
        val routingRulesDeferred = async { getRoutingRules(model.modelType()).getOrElse { emptyList() } }
        val personDeferred = async { personService.get(model.personId).get() }
        val riskDeferred = async { riskService.getByPerson(model.personId).getOrNullIfNotFound()?.riskDescription }

        HealthcareTeamInput(
            personTeamAssociation = personTeamAssociationDeferred.await(),
            balances = balance,
            source = model,
            person = personDeferred.await(),
            risk = riskDeferred.await(),
            routingRules = routingRulesDeferred.await()
        )
    }

}
