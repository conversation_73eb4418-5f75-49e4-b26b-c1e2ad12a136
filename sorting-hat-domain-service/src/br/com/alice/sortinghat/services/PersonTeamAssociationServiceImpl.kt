package br.com.alice.sortinghat.services

import br.com.alice.common.core.PersonId
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.extensions.getOrNullIfNotFound
import br.com.alice.common.extensions.then
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.observability.recordResult
import br.com.alice.common.observability.setAttribute
import br.com.alice.common.service.data.dsl.and
import br.com.alice.common.service.extensions.WithFilterPredicateUsage
import br.com.alice.common.service.extensions.withFilter
import br.com.alice.data.layer.models.PersonTeamAssociation
import br.com.alice.data.layer.models.PersonTeamAssociationStatus
import br.com.alice.data.layer.models.PersonTeamAssociationStatus.ACTIVE
import br.com.alice.data.layer.models.PersonTeamAssociationStatus.INACTIVE
import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.services.PersonTeamAssociationDataService
import br.com.alice.marauders.map.client.RiskService
import br.com.alice.sortinghat.client.PersonTeamAssociationService
import br.com.alice.sortinghat.event.PersonTeamAssociationEvent
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.flatMap
import java.util.UUID

class PersonTeamAssociationServiceImpl(
    private val riskService: RiskService,
    private val personTeamAssociationDataService: PersonTeamAssociationDataService,
    private val kafkaProducerService: KafkaProducerService
) : PersonTeamAssociationService {

    @OptIn(WithFilterPredicateUsage::class)
    override suspend fun getByPersonId(
        personId: PersonId,
        status: PersonTeamAssociationStatus?
    ): Result<PersonTeamAssociation, Throwable> =
        personTeamAssociationDataService.findOne {
            where { this.personId.eq(personId).withFilter(status) { this.status.eq(it) }!! }
        }

    @OptIn(WithFilterPredicateUsage::class)
    override suspend fun getByPersonIds(
        personIds: List<PersonId>,
        status: PersonTeamAssociationStatus?
    ): Result<List<PersonTeamAssociation>, Throwable> =
        personTeamAssociationDataService.find {
            where { this.personId.inList(personIds).withFilter(status) { this.status.eq(it) }!! }
        }

    override suspend fun syncAssociation(
        personId: PersonId,
        teamId: UUID,
        quartetId: UUID?
    ): Result<PersonTeamAssociation, Throwable> = span("syncAssociation") { span ->
        span.setAttribute("person_id", personId)
        span.setAttribute("healthcare_team_id", teamId)
        span.setAttribute("healthcare_additional_team_id", quartetId)

        riskService.getByPerson(personId).getOrNullIfNotFound().let { risk ->
            updateAssociation(personId, teamId, quartetId, risk)
        }.recordResult(span)
    }

    suspend fun inactiveAssociation(personId: PersonId, teamId: UUID) =
        personTeamAssociationDataService.findOne {
            where { this.personId.eq(personId) and this.healthcareTeamId.eq(teamId) }
        }.flatMap {
            personTeamAssociationDataService.update(it.copy(status = INACTIVE))
        }

    suspend fun updateRisk(personId: PersonId, risk: RiskDescription) =
        personTeamAssociationDataService.findOne {
            where { this.personId.eq(personId) }
        }.flatMap {
            personTeamAssociationDataService.update(it.copy(risk = risk))
        }

    private suspend fun publishEvent(association: PersonTeamAssociation) =
        kafkaProducerService.produce(
            PersonTeamAssociationEvent(
                personId = association.personId,
                healthcareTeamId = association.healthcareTeamId,
                additionalTeamId = association.healthcareAdditionalTeamId,
                risk = association.risk
            )
        )

    private suspend fun updateAssociation(personId: PersonId, teamId: UUID, quartetId: UUID?, risk: Risk?) =
        personTeamAssociationDataService.findOne {
            where { this.personId.eq(personId) }
        }.flatMap {
            updatePersonTeamAssociation(
                it.copy(
                    healthcareTeamId = teamId,
                    healthcareAdditionalTeamId = quartetId,
                    risk = risk?.riskDescription,
                    status = ACTIVE
                )
            )
        }.coFoldNotFound { addPersonTeamAssociation(personId, teamId, quartetId, risk) }

    private suspend fun addPersonTeamAssociation(
        personId: PersonId,
        teamId: UUID,
        quartetId: UUID?,
        risk: Risk?
    ) = span("addPersonTeamAssociation") { span ->
        personTeamAssociationDataService.add(
            PersonTeamAssociation(
                personId = personId,
                healthcareTeamId = teamId,
                healthcareAdditionalTeamId = quartetId,
                risk = risk?.riskDescription,
                status = ACTIVE
            )
        ).then { publishEvent(it) }
            .recordResult(span)
    }

    private suspend fun updatePersonTeamAssociation(association: PersonTeamAssociation) =
        span("updatePersonTeamAssociation") { span ->
            personTeamAssociationDataService.update(association)
                .then { publishEvent(it) }
                .recordResult(span)
        }

}
