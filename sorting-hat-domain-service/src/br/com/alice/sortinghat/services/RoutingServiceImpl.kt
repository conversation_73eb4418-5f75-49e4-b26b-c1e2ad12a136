package br.com.alice.sortinghat.services

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.RoutingHistoryModel
import br.com.alice.sortinghat.client.RoutingService
import br.com.alice.sortinghat.inputcreators.InputCreator
import br.com.alice.sortinghat.loadbalances.BalanceLoader
import br.com.alice.sortinghat.models.balances.Balance
import br.com.alice.sortinghat.models.input.RoutingModel
import br.com.alice.sortinghat.models.inputs.Input
import br.com.alice.sortinghat.models.output.OutputModel
import br.com.alice.sortinghat.rules.Rule
import com.github.kittinunf.result.Result

class RoutingServiceImpl(
    private val allRules: List<Rule<Input, OutputModel>>,
    private val allBalanceLoaders: List<BalanceLoader<Balance>>,
    private val allInputCreators: List<InputCreator<Input, RoutingModel, Balance>>
) : RoutingService, Spannable {

    override suspend fun execute(json: String): Result<List<String>, Throwable> = span("execute") { span ->
        coResultOf<List<String>, Throwable> {
            RoutingModel.deserialize(json).let { source ->

                span.setAttribute("model_type", source.modelType().name)

                (getRules(source.modelType()) to getInput(source))
                    .let { (filteredRules, input) ->
                        filteredRules.sortedBy { it.ruleOrder() }.map { it.apply(input) }
                    }.flatten()
                    .map { it.serialize() }
                    .distinct()
            }
        }.recordResult(span)
    }

    private suspend fun getInput(source: RoutingModel): Input = span("getInput") {
        getBalance(source.modelType()).let { balance ->
            getInputCreator(source.modelType())
                .create(source, balance)
        }
    }

    private suspend fun getRules(type: RoutingHistoryModel) = span("getRules") { span ->
        allRules
            .filter { it.modelType() == type }
            .takeIf { it.isNotEmpty() }
            ?.also {
                span.setAttribute("count", it.size.toString())
                span.setAttribute("type", type.name)
            }
            ?: throw NotFoundException("Not found rules for $type")
    }

    private suspend fun getBalance(type: RoutingHistoryModel): List<Balance> =
        allBalanceLoaders.firstOrNull { it.modelType() == type }?.load()
            ?: throw NotFoundException("Not found balance for $type")

    private fun getInputCreator(type: RoutingHistoryModel): InputCreator<Input, RoutingModel, Balance> =
        allInputCreators.firstOrNull { it.modelType() == type }
            ?: throw NotFoundException("Not found input creator for $type")

}
