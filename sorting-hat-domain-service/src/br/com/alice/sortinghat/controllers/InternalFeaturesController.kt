package br.com.alice.sortinghat.controllers

import br.com.alice.common.asyncLayer
import br.com.alice.common.core.PersonId
import br.com.alice.common.coroutine.pmap
import br.com.alice.common.extensions.coResultOf
import br.com.alice.common.foldResponse
import br.com.alice.common.withRootServicePolicy
import br.com.alice.common.withUnauthenticatedTokenWithKey
import br.com.alice.data.layer.SORTING_HAT_DOMAIN_ROOT_SERVICE_NAME
import br.com.alice.sortinghat.loadbalances.channel.ChannelAcuteBalanceLoader
import br.com.alice.sortinghat.services.PersonTeamAssociationServiceImpl
import com.github.kittinunf.result.Result
import java.util.UUID

class InternalFeaturesController(
    private val channelAcuteBalanceLoader: ChannelAcuteBalanceLoader,
    private val personTeamAssociationService: PersonTeamAssociationServiceImpl
) {

    suspend fun debugChannelAcuteBalance() = withSubscribersEnvironment {
        channelAcuteBalanceLoader.debug()
    }.foldResponse()

    suspend fun inactivePersonTeamAssociation(request: InactivePersonTeamAssociationsRequest) =
        withSubscribersEnvironment {
            coResultOf {
                request.associations.pmap {
                    personTeamAssociationService.inactiveAssociation(it.personId, it.teamId).get()
                }
            }
        }.foldResponse()

    private suspend fun withSubscribersEnvironment(func: suspend () -> Result<Any, Throwable>) =
        asyncLayer {
            withRootServicePolicy(SORTING_HAT_DOMAIN_ROOT_SERVICE_NAME) {
                withUnauthenticatedTokenWithKey(SORTING_HAT_DOMAIN_ROOT_SERVICE_NAME) {
                    func.invoke()
                }
            }
        }

}

data class InactivePersonTeamAssociationsRequest(
    val associations: List<InactivePersonTeamAssociationRequest>
)

data class InactivePersonTeamAssociationRequest(
    val personId: PersonId,
    val teamId: UUID
)
