package br.com.alice.zendeskintegrationservice.services.internal

import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.zendesk.transport.ZendeskOrganization
import br.com.alice.zendesk.transport.ZendeskOrganizationRequest
import br.com.alice.zendeskintegrationservice.clients.ZendeskClient
import com.github.kittinunf.result.Result

class ZendeskOrganizationService(
    private val zendeskClient: ZendeskClient
) : Spannable {

    suspend fun upsert(organization: ZendeskOrganizationRequest): Result<ZendeskOrganization, Throwable> =
        span("upsert") { span ->
            zendeskClient.createOrUpdateOrganization(organization).recordResult(span)
        }
}
