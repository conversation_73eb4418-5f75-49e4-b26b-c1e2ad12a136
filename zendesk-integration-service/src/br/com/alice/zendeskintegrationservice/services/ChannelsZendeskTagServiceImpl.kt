package br.com.alice.zendeskintegrationservice.services

import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.extensions.coFoldNotFound
import br.com.alice.common.observability.Spannable
import br.com.alice.common.observability.recordResult
import br.com.alice.data.layer.models.ChannelsZendeskTag
import br.com.alice.data.layer.services.ChannelsZendeskTagDataService
import br.com.alice.zendesk.client.ChannelsZendeskTagService
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.flatMap


class ChannelsZendeskTagServiceImpl(
    private val dataService: ChannelsZendeskTagDataService
): ChannelsZendeskTagService {
    override suspend fun upsert(tag: ChannelsZendeskTag): Result<ChannelsZendeskTag, Throwable> =
        span("upsert") { span ->
            dataService.findByChannelTag(tag.channelTag)
                .fold(
                    {
                        dataService.update(tag.copy(id = it.id))
                    },
                    {
                        if (it is NotFoundException) dataService.add(tag)
                        else it.failure()
                    }
                ).recordResult(span)
        }

    override suspend fun getAll(): Result<List<ChannelsZendeskTag>, Throwable> =
        span("getAll") { span ->
            dataService.getAll().recordResult(span)
        }

    override suspend fun findByChannelTags(tags: List<String>): Result<ChannelsZendeskTag, Throwable> =
        span("findByChannelTags") { span ->
            dataService.findByChannelTags(tags).recordResult(span)
        }

}
