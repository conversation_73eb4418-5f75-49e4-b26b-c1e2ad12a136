package br.com.alice.zendeskintegrationservice.exceptions

import br.com.alice.common.core.exceptions.BadRequestException

open class ControllerException(
    message: String,
    code: String,
    cause: Throwable? = null
) : BadRequestException(message, code, cause)

class SyncedAtWasBeforeLastUpdate(
    message: String = "SyncedAt was before update",
    code: String = "synced_at_was_before_update_error",
    cause: Throwable? = null
) : ControllerException(message, code, cause)

class PersonWithoutCurrentMemberException(
    message: String = "Person without current member does not need to be synced with Zendesk",
    code: String = "person_without_current_member_exception",
    cause: Throwable? = null
) : ControllerException(message, code, cause)

class TicketWithoutConversationIdException(
    message: String = "Ticket without conversation_id",
    code: String = "ticket_without_conversation_id",
    cause: Throwable? = null
) : ControllerException(message, code, cause)

