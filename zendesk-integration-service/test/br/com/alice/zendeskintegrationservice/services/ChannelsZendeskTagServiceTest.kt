package br.com.alice.zendeskintegrationservice.services

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResultAssert
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.services.ChannelsZendeskTagDataService
import br.com.alice.zendeskintegrationservice.helpers.TestModelFactory
import br.com.alice.zendeskintegrationservice.services.ChannelsZendeskTagServiceImpl
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class ChannelsZendeskTagServiceTest {

    private val dataService: ChannelsZendeskTagDataService = mockk()
    private val service = ChannelsZendeskTagServiceImpl(dataService)

    private val tag = TestModelFactory.buildChannelsZendeskTag()

    @Test
    fun `#upsert should create a tag`() = runBlocking {
        coEvery {
            dataService.findByChannelTag(tag.channelTag)
        } returns NotFoundException().failure()

        coEvery { dataService.add(tag) } returns tag.success()

        val result = service.upsert(tag)

        assertThat(result).isSuccessWithData(tag)

        coVerifyOnce {
            dataService.findByChannelTag(any())
            dataService.add(any())
        }

        coVerifyNone { dataService.update(any()) }
    }

    @Test
    fun `#upsert should update a tag`() = runBlocking {
        val tagToUpdate = tag.copy(channelTag = "new_channel_tag")

        coEvery {
            dataService.findByChannelTag("new_channel_tag")
        } returns tag.success()
        coEvery { dataService.update(tagToUpdate) } returns tagToUpdate.success()

        val result = service.upsert(tagToUpdate)

        assertThat(result).isSuccessWithData(tagToUpdate)

        coVerifyOnce {
            dataService.findByChannelTag(any())
            dataService.update(any())
        }

        coVerifyNone { dataService.add(any()) }
    }

    @Test
    fun `#upsert should not update a tag when error`() = runBlocking {
        val tagToUpdate = tag.copy(channelTag = "new_channel_tag")
        coEvery {
            dataService.findByChannelTag("new_channel_tag")
        } returns tag.success()
        
        coEvery { dataService.update(tagToUpdate) } returns InvalidArgumentException("wrong stuff").failure()

        val result = service.upsert(tagToUpdate)

        assertThat(result).isFailureOfType(InvalidArgumentException::class)

        coVerifyOnce {
            dataService.findByChannelTag(any())
            dataService.update(any())
        }

        coVerifyNone { dataService.add(any()) }
    }

    @Test
    fun `#getAll should return all values`() = runBlocking {
        val tag2 = tag.copy(channelTag = "channel_tag_2")

        coEvery {
            dataService.getAll()
        } returns listOf(tag, tag2).success()

        val result = service.getAll()

        assertThat(result).isSuccessWithData(listOf(tag, tag2))

        coVerifyOnce {
            dataService.getAll()
        }

        coVerifyNone { dataService.get(any()) }
    }

    @Test
    fun `#findByChannelTags should return a single tag`() = runBlocking {
        coEvery {
            dataService.findByChannelTags(listOf("triagem_pagamentos_emissão_docs_final", "tag_2"))
        } returns tag.success()

        val result = service.findByChannelTags(listOf("triagem_pagamentos_emissão_docs_final", "tag_2"))

        assertThat(result).isSuccessWithData(tag)

        coVerifyOnce {
            dataService.findByChannelTags(any())
        }

        coVerifyNone { dataService.get(any()) }
        coVerifyNone { dataService.getAll() }
        coVerifyNone { dataService.findByChannelTag(any()) }
    }
}
