package br.com.alice.zendeskintegrationservice.controllers

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.zendesk.client.ChannelsZendeskTagService
import br.com.alice.zendeskintegrationservice.helpers.TestModelFactory
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.BeforeTest
import kotlin.test.Test

class ChannelsZendeskTagControllerTest: RoutesTestHelper() {

    private val service: ChannelsZendeskTagService = mockk()
    private val controller = ChannelsZendeskTagController(service)

    private val channelZendeskTag = TestModelFactory.buildChannelsZendeskTag()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @Test
    fun `#upsertChannelsZendeskTag should fail when service fails`() = runBlocking {
        val request = UpsertChannelZendeskTagRequest(
            channelTag = channelZendeskTag.channelTag,
            zendeskAssignmentTag = channelZendeskTag.zendeskAssignmentTag,
            description = channelZendeskTag.description
        )

        coEvery {
            service.upsert(match{
                it.channelTag == channelZendeskTag.channelTag
                        && it.zendeskAssignmentTag == channelZendeskTag.zendeskAssignmentTag
                        && it.description == channelZendeskTag.description
            })
        } returns InvalidArgumentException().failure()

        post(to = "/upsert_channels_zendesk_tag", body = request) { response ->
            assertThat(response).isBadRequestWithErrorCode("invalid_argument", "Invalid argument")

            coVerifyOnce {
                service.upsert(any())
            }
        }
    }

    @Test
    fun `#upsertChannelsZendeskTag should run successfully`() = runBlocking {
        val request = UpsertChannelZendeskTagRequest(
            channelTag = channelZendeskTag.channelTag,
            zendeskAssignmentTag = channelZendeskTag.zendeskAssignmentTag,
            description = channelZendeskTag.description
        )

        coEvery {
            service.upsert(match{
                it.channelTag == channelZendeskTag.channelTag
                        && it.zendeskAssignmentTag == channelZendeskTag.zendeskAssignmentTag
                        && it.description == channelZendeskTag.description
            })
        } returns channelZendeskTag.success()

        post(to = "/upsert_channels_zendesk_tag", body = request) { response ->
            assertThat(response).isOKWithData(UpsertChannelZendeskTagResponse(
                channelTag = channelZendeskTag.channelTag,
                zendeskAssignmentTag = channelZendeskTag.zendeskAssignmentTag,
                description = channelZendeskTag.description)
            )

            coVerifyOnce {
                service.upsert(any())
            }
        }
    }
}
