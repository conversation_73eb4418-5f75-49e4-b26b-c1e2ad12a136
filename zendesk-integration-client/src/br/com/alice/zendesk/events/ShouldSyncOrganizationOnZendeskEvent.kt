package br.com.alice.zendesk.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.ZendeskExternalReference
import br.com.alice.zendesk.SERVICE_NAME
import java.time.LocalDateTime
import java.util.UUID

data class ShouldSyncOrganizationOnZendeskEvent(
    val modelId: UUID,
    val entityModelType: ZendeskExternalReference.OriginModel,
    val entityUpdatedAt: LocalDateTime
): NotificationEvent<ShouldSyncOrganizationOnZendeskEvent.Payload> (
    name = name,
    producer = SERVICE_NAME,
    payload = Payload(
        modelId = modelId,
        entityModelType = entityModelType,
        entityUpdatedAt = entityUpdatedAt
    )
){
    companion object {
        const val name = "SHOULD-SYNC-ORGANIZATION-ON-ZENDESK"
    }

    data class Payload(
        val modelId: UUID,
        val entityModelType: ZendeskExternalReference.OriginModel,
        val entityUpdatedAt: LocalDateTime
    )
}

