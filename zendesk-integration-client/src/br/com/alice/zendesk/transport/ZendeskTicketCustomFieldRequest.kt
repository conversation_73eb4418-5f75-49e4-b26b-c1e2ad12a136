package br.com.alice.zendesk.transport

import java.math.BigInteger

data class ZendeskTicketCustomFieldRequest(
    val id: BigInteger,
    val customFields: List<ZendeskTicketCustomField>
)

data class ZendeskTicketCustomField(
    val id: BigInteger,
    val value: String
)

data class ZendeskTicket(
    val id: BigInteger
)

data class ChannelsZendeskTicketData(
    val requesterPersonId: String,
    val subject: String,
    val zendeskTag: String,
    val channelId: String,
    val channelTag: String,
    val context: String
)
