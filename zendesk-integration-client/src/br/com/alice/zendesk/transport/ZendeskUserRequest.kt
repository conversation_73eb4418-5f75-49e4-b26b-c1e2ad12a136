package br.com.alice.zendesk.transport

import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.Brand
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.Product
import br.com.alice.data.layer.models.ProductType
import com.google.gson.annotations.SerializedName
import java.util.UUID

const val fixedRole = "end-user"

data class ZendeskUserRequest(
    val externalId: UUID,
    val name: String,
    val alias: String,
    val phone: String?,
    val organizationId: String?,
    val role: String = fixedRole,
    val userFields: UserFields
) {
    constructor(person: Person, externalId: UUID, organizationId: String?, memberStatus: MemberStatus,
                billingAccountablePartyEmail: String, product: Product) : this(
        externalId = externalId,
        name = person.sanitizedSocialName(),
        alias = person.sanitizedSocialName(),
        phone = person.phoneNumber,
        organizationId = organizationId,
        userFields = UserFields.MemberFields(
            nickName = person.nickName,
            registerName = person.sanitizedRegisterName(),
            nationalId = person.nationalId.onlyDigits(),
            userEmail = person.email,
            billingAccountablePartyEmail = billingAccountablePartyEmail,
            productTitle = product.title,
            productDisplayName = product.displayName,
            hasPriority = person.hasPriorityPassTag,
            productBrand = product.brand!!,
            productType = product.type,
            memberStatus = memberStatus,
        )
    )
}

sealed class UserFields {
    abstract val profile: String

    data class MemberFields(
        @SerializedName("apelido")
        val nickName: String?,
        @SerializedName("nome")
        val registerName: String,
        @SerializedName("cpf")
        val nationalId: String,
        @SerializedName("critico")
        val hasPriority: Boolean,
        @SerializedName("email")
        val userEmail: String,
        @SerializedName("email_fin")
        val billingAccountablePartyEmail: String,
        @SerializedName("produto")
        val productTitle: String,
        @SerializedName("produto_venda")
        val productDisplayName: String?,
        @SerializedName("brand")
        val productBrand: Brand,
        @SerializedName("tipo_produto")
        val productType: ProductType,
        @SerializedName("status")
        val memberStatus: MemberStatus,
        @SerializedName("perfil")
        override val profile: String = UserProfile.MEMBER.description,
    ) : UserFields()
}

enum class UserProfile(val description: String) {
    MEMBER("membro"),
}

data class ZendeskUser(
    val id: Long
)

private fun Person.sanitizedSocialName() =
    this.fullSocialName.replace("|", "-").trim().plus(if (this.nickName.isNullOrBlank()) "" else " (${this.nickName})")

private fun Person.sanitizedRegisterName() = this.fullRegisterName.replace("|", "-").trim()
