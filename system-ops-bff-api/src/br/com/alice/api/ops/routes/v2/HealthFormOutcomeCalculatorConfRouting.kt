package br.com.alice.api.ops.routes.v2

import br.com.alice.api.ops.controllers.healthlogics.HealthFormOutcomeCalculatorConfController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.healthFormOutcomeCalculatorConf() {

    authenticate {
        val controller by inject<HealthFormOutcomeCalculatorConfController>()

        route("health_form_calculator_conf") {
            get("/{id}") { coHandler("id", controller::get) }
            get { coHandler(controller::index) }
            post { coHandler(controller::create) }
            put("/{id}/{status}"){ coHandler("id", "status", controller::update) }
        }
    }
}
