package br.com.alice.api.ops.routes

import br.com.alice.api.ops.controllers.fhir.ProviderAccessController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.fhirBackofficeRoutes() {
    authenticate {
        val providerAccessController by inject<ProviderAccessController>()
        route("fhir") {
            route("/provider_access") {
                get { coHandler(providerAccessController::index) }
                get("/{id}") { coHandler("id", providerAccessController::getById) }
                post { coHandler(providerAccessController::create) }
                put("/{id}") { coHandler("id", providerAccessController::update) }
            }
        }
    }
}
