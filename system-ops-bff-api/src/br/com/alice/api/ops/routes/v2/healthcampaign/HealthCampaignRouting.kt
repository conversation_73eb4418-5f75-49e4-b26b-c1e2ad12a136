package br.com.alice.api.ops.routes.v2.healthcampaign

import br.com.alice.api.ops.controllers.healthcampaign.HealthCampaignController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import br.com.alice.common.multipartHandler
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.route

fun Routing.healthCampaignsRoutes() {

    authenticate {
        val healthCampaignController by inject<HealthCampaignController>()

        route("/health_campaign") {
            post("/{campaignId}") { multipartHandler("campaignId", healthCampaignController::upload) }
            get("/") { coHandler(healthCampaignController::getCampaigns) }
        }
    }
}
