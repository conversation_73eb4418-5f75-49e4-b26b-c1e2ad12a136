package br.com.alice.api.ops.routes.v2

import br.com.alice.api.ops.controllers.HealthDemandMonitoringController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.healthDemandMonitoringRoutes() {
    authenticate {
        val controller by inject<HealthDemandMonitoringController>()


        route("outcome_health_condition") {
            get("/") { coHandler(controller::index) }
            post("/") { co<PERSON>and<PERSON>(controller::create) }
            get("/{id}") { coHandler("id", controller::get) }
            put("/{id}") { coHandler("id", controller::update) }
        }
    }
}
