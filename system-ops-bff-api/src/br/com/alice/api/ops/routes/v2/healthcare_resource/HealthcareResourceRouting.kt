package br.com.alice.api.ops.routes.v2.healthcare_resource

import br.com.alice.api.ops.controllers.healthcare_resource.HealthcareBundleController
import br.com.alice.api.ops.controllers.healthcare_resource.HealthcareResourceGroupController
import br.com.alice.common.coHandler
import br.com.alice.common.extensions.inject
import io.ktor.server.auth.authenticate
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.route

fun Routing.healthcareResourceRouting() {
    authenticate {
        val healthcareResourceGroupController by inject<HealthcareResourceGroupController>()

        route("healthcare_resource_group") {
            post("/") {
                coHandler(healthcareResourceGroupController::add)
            }

            get("/") {
                coHandler(healthcareResourceGroupController::list)
            }
        }

        val healthcareBundleController by inject<HealthcareBundleController>()

        route("healthcare_bundles") {
            put("/status") {
                coHandler(healthcareBundleController::updateStatus)
            }
        }

        route("healthcare_bundle") {
            post("/") {
                coHandler(healthcareBundleController::add)
            }

            post("/create_by_bundles") {
                coHandler(healthcareBundleController::addByBundles)
            }

            get("/") {
                coHandler(healthcareBundleController::list)
            }

            get("/{id}") {
                coHandler("id", healthcareBundleController::get)
            }

            get("/{id}/associated_resource") {
                coHandler("id", healthcareBundleController::getAssociatedResource)
            }

            get("/{id}/resources") {
                coHandler("id", healthcareBundleController::getResources)
            }

            get("/{id}/groups") {
                coHandler("id", healthcareBundleController::getGroups)
            }

            put("/{id}") {
                coHandler("id", healthcareBundleController::update)
            }

            get("/provider_name") {
                coHandler(healthcareBundleController::listProviderName)
            }

            delete("/{id}") {
                coHandler("id", healthcareBundleController::delete)
            }
        }
    }
}
