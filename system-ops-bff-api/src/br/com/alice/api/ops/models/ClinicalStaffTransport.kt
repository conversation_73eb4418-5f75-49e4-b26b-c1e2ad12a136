package br.com.alice.api.ops.models

import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.PhoneNumber
import br.com.alice.data.layer.models.Qualification
import br.com.alice.data.layer.models.SpecialistAppointmentType
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.StructuredAddress
import java.util.UUID

data class ClinicalStaffResponse(
    val id: UUID,
    val name: String,
    val gender: Gender? = null,
    val specialtyId: UUID?,
    val subSpecialtyIds: List<UUID> = emptyList(),
    val email: String?,
    val councilState: String?,
    val councilNumber: String?,
    val phones: List<PhoneNumber>,
    val qualifications: List<Qualification>,
    val imageUrl: String?,
    val education: List<String> = emptyList(),
    val tier: SpecialistTier? = null,
    val providerUnitIds: List<UUID> = emptyList(),
    val scheduleAvailabilityDays: Int? = null,
    val appointmentTypes: List<SpecialistAppointmentType> = emptyList(),
    val curiosity: String?,
    val showOnApp: Boolean,
    val status: SpecialistStatus?,
    val urlSlug: String? = null,
    val addressList: List<AddressRequest> = emptyList(),
    val staffId: UUID?
)

data class ClinicalStaffWithAddresses(
    val healthProfessional: HealthProfessional,
    val addresses: List<StructuredAddress>? = null
)
