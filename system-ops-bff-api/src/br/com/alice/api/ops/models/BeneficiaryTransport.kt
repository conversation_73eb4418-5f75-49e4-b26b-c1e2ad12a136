package br.com.alice.api.ops.models

import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.Beneficiary
import br.com.alice.data.layer.models.BeneficiaryCancelationReason
import br.com.alice.data.layer.models.BeneficiaryContractType
import br.com.alice.data.layer.models.BeneficiaryOnboardingFlowType
import br.com.alice.data.layer.models.BeneficiaryOnboardingPhase
import br.com.alice.common.BeneficiaryType
import br.com.alice.data.layer.models.MemberStatus
import br.com.alice.data.layer.models.ParentBeneficiaryRelationType
import br.com.alice.data.layer.models.Person
import java.util.UUID

data class BeneficiaryRequest(
    val parentBeneficiary: UUID? = null,
    val companyId: UUID,
    val contractType: BeneficiaryContractType? = null,
    val parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
    val parentBeneficiaryRelatedAt: String? = null,
    val activatedAt: String,
    val hiredAt: String? = null,
    val cnpj: String?,
    val subcontractId: UUID? = null,

    // beneficiaryOnboarding fields
    val flowType: BeneficiaryOnboardingFlowType,

    //cassiMember fields
    val cassiAccountNumber: String? = null,
    val cassiStartDate: String? = null,
    val cassiExpirationDate: String? = null,
)

data class BeneficiaryAndPersonRequestBatch(
    val batch: List<BeneficiarySimpleAndPersonBatchRequest>
)

data class BeneficiarySimpleAndPersonBatchRequest(
    // beneficiary fields
    val parentNationalId: String? = null,
    val companyCnpj: String,
    val subContractId: UUID? = null,
    val contractType: BeneficiaryContractType? = null,
    val parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
    val activatedAt: String,
    val cnpj: String?,
    val hiredAt: String? = null,

    // person fields
    val email: String,
    val nationalId: String,
    val firstName: String,
    val lastName: String,
    val socialName: String? = null,
    val addressPostalCode: String,
    val addressStreet: String,
    val addressNumber: Int,
    val addressComplement: String? = null,
    val addressNeighborhood: String? = null,
    val addressCity: String,
    val addressState: String,
    val sex: Sex? = null,
    val dateOfBirth: String? = null,
    val phoneNumber: String? = null,
    val tags: String? = null,
    val mothersName: String,

    // beneficiaryOnboarding fields
    val flowType: BeneficiaryOnboardingFlowType,
    val initialProductName: String?,

    //cassiMember fields
    val cassiAccountNumber: String?,
    val cassiStartDate: String?,
    val cassiExpirationDate: String?,
)

data class BeneficiaryIdsRequest(
    val beneficiaryIds: List<UUID>,
    val bypass_validation: Boolean? = false,
)

data class BeneficiaryAndPersonRequest(
    // beneficiary fields
    val parentBeneficiary: UUID? = null,
    val companyId: UUID,
    val contractType: BeneficiaryContractType? = null,
    val parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
    val parentBeneficiaryRelatedAt: String? = null,
    val activatedAt: String,
    val cnpj: String?,
    val hiredAt: String? = null,
    val subcontractId: UUID? = null,

    // person fields
    val email: String,
    val nationalId: String,
    val firstName: String,
    val lastName: String,
    val socialName: String? = null,
    val addressPostalCode: String? = null,
    val addressStreet: String,
    val addressNumber: Int,
    val addressComplement: String? = null,
    val addressNeighborhood: String? = null,
    val addressCity: String,
    val addressState: String,
    val sex: Sex? = null,
    val dateOfBirth: String? = null,
    val phoneNumber: String? = null,
    val tags: String? = null,
    val mothersName: String,

    // beneficiaryOnboarding fields
    val flowType: BeneficiaryOnboardingFlowType,
    val initialProductId: UUID?,

    //cassiMember fields
    val cassiAccountNumber: String?,
    val cassiStartDate: String?,
    val cassiExpirationDate: String?,
)

data class BeneficiaryInfoResponse(
    // Beneficiary
    val id: UUID,
    val parentBeneficiary: UUID? = null,
    val personId: UUID,
    val companyId: UUID,
    val type: BeneficiaryType,
    val contractType: BeneficiaryContractType? = null,
    val parentBeneficiaryRelationType: ParentBeneficiaryRelationType? = null,
    val parentBeneficiaryRelatedAt: String?,
    val activatedAt: String,
    val hiredAt: String? = null,
    val canceledAt: String?,
    val canceledReason: BeneficiaryCancelationReason?,
    val canceledDescription: String?,
    val hasContributed: Boolean? = false,
    val cnpj: String?,
    val subcontractId: UUID? = null,

    // BeneficiaryOnboarding
    val initialProductId: UUID?,
    val flowType: BeneficiaryOnboardingFlowType?,
    val currentPhase: BeneficiaryOnboardingPhase?,

    // Person
    val email: String,
    val nationalId: String,
    val firstName: String,
    val lastName: String,
    val socialName: String? = null,
    val addressPostalCode: String?,
    val addressStreet: String?,
    val addressNumber: Int?,
    val addressComplement: String?,
    val addressNeighborhood: String?,
    val addressCity: String?,
    val addressState: String?,
    val sex: Sex? = null,
    val dateOfBirth: String? = null,
    val phoneNumber: String? = null,
    val mothersName: String? = null,
    val tags: String? = null,

    val member: BeneficiaryMemberInfo? = null,

    val currentFlowType: BeneficiaryOnboardingFlowType?, // TODO: Remover após front migrar para `flowType`
    val billingAccountablePartyId: UUID? = null,

    val cassiAccountNumber: String? = null,
    val cassiStartDate: String? = null,
    val cassiExpirationDate: String? = null,
    val cassiMemberId: UUID? = null,

    val hubspotDealId: String? = null,
)

data class BeneficiaryMemberInfo(
    val status: MemberStatus?,
)

data class BeneficiaryBatchResponse(val failures: Map<String, String>)

data class BeneficiaryWithPerson(
    val beneficiary: Beneficiary,
    val person: Person
)

data class CancelMemberActivationRequest(
    val beneficiaryIds: List<UUID>,
)
