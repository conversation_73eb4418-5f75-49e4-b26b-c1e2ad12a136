package br.com.alice.api.ops.models

import br.com.alice.data.layer.models.ProductBundleType
import br.com.alice.data.layer.models.SpecialtyTiers
import java.util.UUID

data class ProductBundleRequest(
    val name: String,
    val providerIds: List<ProductBundleIdRequest>? = null,
    val specialistIds: List<ProductBundleIdRequest>? = null,
    val externalSpecialistIds: List<ProductBundleIdRequest>? = null,
    val specialtyTiers: List<SpecialtyTiers>? = null,
    val type: ProductBundleType,
    val imageUrl: String?,
    val priceScale: Int,
    val active: Boolean,
)

data class ProductBundleResponse(
    val id: UUID,
    val name: String,
    val providerIds: List<ProductBundleIdRequest>? = null,
    val specialistIds: List<ProductBundleIdRequest>? = null,
    val externalSpecialistIds: List<ProductBundleIdRequest>? = null,
    val specialtyTiers: List<SpecialtyTiers>? = null,
    val type: ProductBundleType,
    val imageUrl: String?,
    val priceScale: Int,
    val active: Boolean,
)

data class ProductBundleIdRequest(
    val id: String,
)

data class SpecialistResponse(
    val id: UUID,
    val name: String,
    val email: String? = null,
)
