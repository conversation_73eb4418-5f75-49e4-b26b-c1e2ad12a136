package br.com.alice.api.ops.models

import br.com.alice.data.layer.models.CsatComment
import br.com.alice.data.layer.models.CsatQuestion
import br.com.alice.data.layer.models.CsatRatingOption

data class CsatTemplateRequest(
    val title: String,
    val type: String,
    val rating: List<CsatRatingOption> = emptyList(),
    val questions: List<CsatQuestion> = emptyList(),
    val comments: CsatComment,
    val buttonLabel: String
)
