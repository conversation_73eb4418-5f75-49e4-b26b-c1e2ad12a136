package br.com.alice.api.ops.models

import br.com.alice.common.Brand
import java.util.UUID

data class SiteAccreditedNetworkResponse(
    val id: String,
    val title: String?,
    val brand: Brand? = Brand.ALICE,
    val active: Boolean,
    val products: List<SiteAccreditedNetworkProductResponse>,
    val bundles: List<SiteAccreditedNetworkBundlesResponse>,
)

data class SiteAccreditedNetworkProductResponse(
    val id: UUID,
    val title: String,
)

data class SiteAccreditedNetworkBundlesResponse(
    val id: UUID,
    val title: String,
)

data class SiteAccreditedNetworkRequest(
    val title: String?,
    val brand: String?,
    val active: String?,
    val productIds: List<UUID>?,
    val bundleIds: List<UUID>?,
)
