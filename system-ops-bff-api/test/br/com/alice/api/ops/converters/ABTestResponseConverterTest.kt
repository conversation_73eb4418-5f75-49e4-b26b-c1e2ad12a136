package br.com.alice.api.ops.converters

import br.com.alice.api.ops.models.ABTestResponse
import br.com.alice.api.ops.models.DistributionPayload
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ABTest
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.FeatureType
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.Test
import kotlin.test.assertFailsWith


class ABTestResponseConverterTest {

    @Test
    fun `#convert ABTest list to ABTestResponse list`() {
        val featureConfig1 = TestModelFactory.buildFeatureConfig(
            type = FeatureType.DISTRIBUTION,
            value = "V0:0.2,V1:0.8"
        )
        val featureConfig2 = TestModelFactory.buildFeatureConfig(
            type = FeatureType.DISTRIBUTION,
            value = "qualquerCoisa:1"
        )
        val abTest = ABTest(
            id = featureConfig1.id,
            key = "Test 4",
            featureConfig = featureConfig1,
            description = "Test 4 description",
            namespace = FeatureNamespace.AOS,
            isPublic = true,
            active = true
        )
        val abTest2 = ABTest(
            id = featureConfig2.id,
            key = "Test 5",
            featureConfig = featureConfig2,
            description = "Test 5 description",
            namespace = FeatureNamespace.AOS,
            isPublic = true,
            active = true
        )
        val expected = listOf(
            ABTestResponse(
                id = abTest.id,
                key = abTest.key,
                description = abTest.description,
                distribution = listOf(
                    DistributionPayload(key = "V0", percentage = 20),
                    DistributionPayload(key = "V1", percentage = 80),
                ),
                namespace = FeatureNamespace.AOS,
                isPublic = true,
                active = true
            ),
            ABTestResponse(
                id = abTest2.id,
                key = abTest2.key,
                description = abTest2.description,
                distribution = listOf(
                    DistributionPayload(key = "qualquerCoisa", percentage = 100)
                ),
                namespace = FeatureNamespace.AOS,
                isPublic = true,
                active = true
            )
        )

        val result = ABTestResponseConverter.convert(listOf(abTest, abTest2))

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#convert ABTest to ABTestResponse`() {
        val featureConfig = TestModelFactory.buildFeatureConfig(
            type = FeatureType.DISTRIBUTION,
            value = "V0:0.2,V1:0.8")
        val abTest = ABTest(
            id = featureConfig.id,
            key = "Test 3",
            featureConfig = featureConfig ,
            description = "Test 3 description",
            namespace = FeatureNamespace.AOS,
            isPublic = true,
            active = true
        )

        val expected = ABTestResponse(
            id = abTest.id,
            key = abTest.key,
            description = abTest.description,
            distribution = listOf(
                DistributionPayload(key = "V0", percentage = 20),
                DistributionPayload(key = "V1", percentage = 80),
            ),
            namespace = FeatureNamespace.AOS,
            isPublic = true,
            active = true
        )

        val result = ABTestResponseConverter.convert(abTest)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#convert ABTest to ABTestResponse ignoring comma at the end`() {
        val featureConfig = TestModelFactory.buildFeatureConfig(
            type = FeatureType.DISTRIBUTION,
            value = "V0:0.2,V1:0.8,")
        val abTest = ABTest(
            id = featureConfig.id,
            key = "Test 3",
            featureConfig = featureConfig,
            description = "Test 3 description",
            namespace = FeatureNamespace.AOS,
            isPublic = true,
            active = true
        )

        val expected = ABTestResponse(
            id = abTest.id,
            key = abTest.key,
            description = abTest.description,
            distribution = listOf(
                DistributionPayload(key = "V0", percentage = 20),
                DistributionPayload(key = "V1", percentage = 80),
            ),
            namespace = FeatureNamespace.AOS,
            isPublic = true,
            active = true
        )

        val result = ABTestResponseConverter.convert(abTest)

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#convert throws InvalidFeatureConfigType when FeatureType is different of DISTRIBUTION`() {
        val abTest = ABTest(
            id = RangeUUID.generate(),
            key = "Test 1",
            featureConfig = TestModelFactory.buildFeatureConfig(type = FeatureType.BOOLEAN),
            description = "Test 1 description",
            namespace = FeatureNamespace.AOS,
            isPublic = true,
            active = true
        )

        assertFailsWith(InvalidFeatureConfigType::class) { ABTestResponseConverter.convert(abTest) }
    }

    @Test
    fun `#convert throws IncorrectFeatureConfigValueFormatException when FeatureConfig value is incorrectly formatted`() {
        val abTest = ABTest(
            id = RangeUUID.generate(),
            key = "Test 2",
            featureConfig = TestModelFactory.buildFeatureConfig(
                type = FeatureType.DISTRIBUTION,
                value = "WrongFormat"),
            description = "Test 2 description",
            namespace = FeatureNamespace.AOS,
            isPublic = true,
            active = true
        )

        assertFailsWith(IncorrectFeatureConfigValueFormatException::class) { ABTestResponseConverter.convert(abTest) }
    }
}
