package br.com.alice.api.ops.converters

import br.com.alice.api.ops.models.MemberOnboardingActionRequest
import br.com.alice.api.ops.models.MemberOnboardingOptInRequest
import br.com.alice.api.ops.models.MemberOnboardingStepRequest
import br.com.alice.api.ops.models.MemberOnboardingTemplateRequest
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MemberOnboardingOptIn
import br.com.alice.data.layer.models.MemberOnboardingStep
import org.assertj.core.api.Assertions
import kotlin.test.Test

class MemberOnboardingConverterTest {
    private val optIns = MemberOnboardingOptIn(
        text = "",
        link = "alice.com.br"
    )
    private val memberOnboardingTemplate = TestModelFactory.buildMemberOnboardingTemplate()
    private val memberOnboardingAction = TestModelFactory.buildMemberOnboardingAction()

    private val memberOnboardingLastStep = TestModelFactory.buildMemberOnboardingStep(
        memberOnboardingTemplateId = memberOnboardingTemplate.id,
        actions = listOf(memberOnboardingAction.id),
        type = MemberOnboardingStep.MemberOnboardingStepType.END,
        next = null,
        widgetComponent = null
    )

    private val memberOnboardingHighRiskStep = TestModelFactory.buildMemberOnboardingStep(
        memberOnboardingTemplateId = memberOnboardingTemplate.id,
        actions = listOf(memberOnboardingAction.id),
        type = MemberOnboardingStep.MemberOnboardingStepType.HIGH_RISK,
        next = memberOnboardingLastStep.id,
        widgetComponent = null
    )

    private val memberOnboardingLowRiskStep = TestModelFactory.buildMemberOnboardingStep(
        memberOnboardingTemplateId = memberOnboardingTemplate.id,
        actions = listOf(memberOnboardingAction.id),
        type = MemberOnboardingStep.MemberOnboardingStepType.LOW_RISK,
        next = memberOnboardingLastStep.id,
        widgetComponent = null
    )

    private val memberOnboardingMidStep = TestModelFactory.buildMemberOnboardingStep(
        memberOnboardingTemplateId = memberOnboardingTemplate.id,
        actions = listOf(memberOnboardingAction.id),
        type = MemberOnboardingStep.MemberOnboardingStepType.MID,
        next = null,
        widgetComponent = null
    )

    private val memberOnboardingStep = TestModelFactory.buildMemberOnboardingStep(
        memberOnboardingTemplateId = memberOnboardingTemplate.id,
        actions = listOf(memberOnboardingAction.id),
        optIns = listOf(optIns),
        next = memberOnboardingMidStep.id
    )

    private val memberOnboardingActionRequest = MemberOnboardingActionRequest(
        id = memberOnboardingAction.id,
        icon = memberOnboardingAction.icon,
        actionUrl = memberOnboardingAction.actionUrl,
        label = memberOnboardingAction.label,
        isBackVisible = memberOnboardingAction.isBackVisible
    )

    private val optInsRequest = MemberOnboardingOptInRequest(
        text = "",
        link = "alice.com.br"
    )

    private val memberOnboardingLastStepExpected = MemberOnboardingStepRequest(
        id = memberOnboardingLastStep.id,
        imageUrl = memberOnboardingLastStep.imageUrl,
        title = memberOnboardingLastStep.title,
        key = memberOnboardingLastStep.key,
        description = memberOnboardingLastStep.description,
        actions = listOf(memberOnboardingActionRequest),
        next = memberOnboardingLastStep.next,
        memberOnboardingTemplateId = memberOnboardingLastStep.memberOnboardingTemplateId,
        type = memberOnboardingLastStep.type
    )

    private val memberOnboardingHighRiskStepExpected = MemberOnboardingStepRequest(
        id = memberOnboardingHighRiskStep.id,
        imageUrl = memberOnboardingHighRiskStep.imageUrl,
        title = memberOnboardingHighRiskStep.title,
        key = memberOnboardingHighRiskStep.key,
        description = memberOnboardingHighRiskStep.description,
        actions = listOf(memberOnboardingActionRequest),
        next = memberOnboardingHighRiskStep.next,
        memberOnboardingTemplateId = memberOnboardingHighRiskStep.memberOnboardingTemplateId,
        type = memberOnboardingHighRiskStep.type
    )

    private val memberOnboardingLowRiskStepExpected = MemberOnboardingStepRequest(
        id = memberOnboardingLowRiskStep.id,
        imageUrl = memberOnboardingLowRiskStep.imageUrl,
        title = memberOnboardingLowRiskStep.title,
        key = memberOnboardingLowRiskStep.key,
        description = memberOnboardingLowRiskStep.description,
        actions = listOf(memberOnboardingActionRequest),
        next = memberOnboardingLowRiskStep.next,
        memberOnboardingTemplateId = memberOnboardingLowRiskStep.memberOnboardingTemplateId,
        type = memberOnboardingLowRiskStep.type
    )

    private val memberOnboardingMidStepExpected = MemberOnboardingStepRequest(
        id = memberOnboardingMidStep.id,
        imageUrl = memberOnboardingMidStep.imageUrl,
        title = memberOnboardingMidStep.title,
        key = memberOnboardingMidStep.key,
        description = memberOnboardingMidStep.description,
        actions = listOf(memberOnboardingActionRequest),
        next = memberOnboardingMidStep.next,
        memberOnboardingTemplateId = memberOnboardingMidStep.memberOnboardingTemplateId,
        type = memberOnboardingMidStep.type
    )

    private val memberOnboardingStepExpected = MemberOnboardingStepRequest(
        id = memberOnboardingStep.id,
        imageUrl = memberOnboardingStep.imageUrl,
        title = memberOnboardingStep.title,
        key = memberOnboardingStep.key,
        description = memberOnboardingStep.description,
        actions = listOf(memberOnboardingActionRequest),
        next = memberOnboardingStep.next,
        memberOnboardingTemplateId = memberOnboardingStep.memberOnboardingTemplateId,
        type = memberOnboardingStep.type,
        optIns = listOf(optInsRequest),
        widgetComponent = MemberOnboardingStep.MemberOnboardingWidgetComponent.SCORE_MAGENTA
    )

    private val memberOnboardingTemplateExpected = MemberOnboardingTemplateRequest(
        id = memberOnboardingTemplate.id,
        name = memberOnboardingTemplate.name,
        type = memberOnboardingTemplate.type,
        active = true,
        steps = listOf(
            memberOnboardingStepExpected,
            memberOnboardingMidStepExpected,
            memberOnboardingHighRiskStepExpected,
            memberOnboardingLowRiskStepExpected,
            memberOnboardingLastStepExpected
        )
    )

    private val justMemberOnboardingTemplateExpected = MemberOnboardingTemplateRequest(
        id = memberOnboardingTemplate.id,
        name = memberOnboardingTemplate.name,
        type = memberOnboardingTemplate.type,
        active = true,
    )

    @Test
    fun `#MemberOnboardingTemplate_toMemberOnboardingTemplateRequest - convert correctly`() {
        val unorderedSteps = listOf(
            memberOnboardingLowRiskStep,
            memberOnboardingHighRiskStep,
            memberOnboardingStep,
            memberOnboardingLastStep,
            memberOnboardingMidStep
        )
        val result = memberOnboardingTemplate.toMemberOnboardingTemplateRequest(
            steps = unorderedSteps,
            actions = listOf(memberOnboardingAction)
        ).get()

        Assertions.assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("id")
            .isEqualTo(memberOnboardingTemplateExpected)
    }

    @Test
    fun `#MemberOnboardingTemplate_toMemberOnboardingTemplateRequest - convert correctly just template`() {
        val result = memberOnboardingTemplate.toMemberOnboardingTemplateRequest().get()

        Assertions.assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("id")
            .isEqualTo(justMemberOnboardingTemplateExpected)
    }
}
