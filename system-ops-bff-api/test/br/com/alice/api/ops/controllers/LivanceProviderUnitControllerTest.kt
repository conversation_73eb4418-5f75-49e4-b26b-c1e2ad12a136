package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.schedule.client.LivanceService
import br.com.alice.schedule.model.LivanceLocation
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.mockk
import kotlin.test.BeforeTest
import kotlin.test.Test

class LivanceProviderUnitControllerTest : ControllerTestHelper() {

    private val livanceService: LivanceService = mockk()

    private val controller = LivanceProviderUnitController(
        livanceService
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @Test
    fun `autocompleteAddress should return autocomplete response`() {
        val locations = listOf(
            LivanceLocation(
                id = "1",
                name = "<PERSON><PERSON><PERSON> (AGL)",
                locationCode = "AGL"
            )
        )

        coEvery { livanceService.getLocations() } returns locations.success()

        authenticatedAs(idToken, staff) {
            get("/livance/locations") { response ->
                ResponseAssert.assertThat(response).isOKWithData(locations)
            }
        }

        coVerifyOnce { livanceService.getLocations() }
    }

}

