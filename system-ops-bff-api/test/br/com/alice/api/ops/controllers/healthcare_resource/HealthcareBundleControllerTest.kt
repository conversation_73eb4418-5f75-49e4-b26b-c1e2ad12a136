package br.com.alice.api.ops.controllers.healthcare_resource

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareBundleCategory
import br.com.alice.data.layer.models.HealthcareBundleStatus
import br.com.alice.data.layer.models.HealthcareResource
import br.com.alice.exec.indicator.client.HealthcareBundleListFilters
import br.com.alice.exec.indicator.client.HealthcareBundleListWithCount
import br.com.alice.exec.indicator.client.HealthcareBundleService
import br.com.alice.exec.indicator.client.HealthcareResourceGroupService
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.provider.client.ProviderUnitGroupService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthcareBundleControllerTest : ControllerTestHelper() {

    private val healthcareBundleService: HealthcareBundleService = mockk()
    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val healthcareResourceGroupService: HealthcareResourceGroupService = mockk()
    private val providerUnitGroupService: ProviderUnitGroupService = mockk()

    private val healthcareBundleController = HealthcareBundleController(
        healthcareBundleService,
        healthcareResourceService,
        healthcareResourceGroupService,
        providerUnitGroupService
    )

    private val now = LocalDateTime.now()
    private val id = RangeUUID.generate()
    private val providerUnit = TestModelFactory.buildProviderUnit()
    private val healthcareResource = TestModelFactory.buildHealthcareResource()
    private val providerUnitGroup = TestModelFactory.buildProviderUnitGroup()

    private val healthcareBundle = TestModelFactory.buildHealthcareBundle(
        name = "Apendicectomia",
        code = "123",
        primaryTuss = "456",
        primaryTussHealthcareResourceId = healthcareResource.id,
        status = HealthcareBundleStatus.ACTIVE,
        category = HealthcareBundleCategory.HEALTH_INSTITUTION,
        hasMedicalFees = true,
        validAfter = now.toLocalDate(),
        providerName = providerUnit.name,
        providerUnitGroupId = providerUnitGroup.id
    ).copy(
        id = id,
        createdAt = now,
        updatedAt = now,
        validAfter = now.toLocalDate(),
    )

    private val healthcareBundles = generateSequence { healthcareBundle }
        .take(5)
        .map { it.copy(id = RangeUUID.generate()) }
        .toList()

    private val serviceTypeId = RangeUUID.generate()

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { healthcareBundleController }
    }

    @Test
    fun `#add should add healthcare bundle`() = runBlocking {
        val request = HealthcareBundleRequest(
            name = healthcareBundle.name,
            code = healthcareBundle.code,
            primaryTuss = healthcareBundle.primaryTuss!!,
            status = healthcareBundle.status,
            hasMedicalFees = true,
            validAfter = healthcareBundle.validAfter,
            providerName = healthcareBundle.providerName,
            primaryTussHealthcareResourceId = healthcareBundle.primaryTussHealthcareResourceId,
            providerUnitGroupId = healthcareBundle.providerUnitGroupId,
        )

        mockRangeUUID(id) {
            mockLocalDateTime(now) {
                authenticatedAs(idToken, staff) {
                    coEvery {
                        providerUnitGroupService.get(providerUnitGroup.id)
                    } returns providerUnitGroup.success()

                    coEvery {
                        healthcareBundleService.add(healthcareBundle)
                    } returns healthcareBundle.success()

                    post(to = "/healthcare_bundle", body = request) { response ->
                        ResponseAssert.assertThat(response).isOKWithData(healthcareBundle)
                    }

                    coVerifyOnce { healthcareBundleService.add(any()) }
                }
            }
        }
    }

    @Test
    fun `#update should update some healthcare bundle fields`() = runBlocking {
        val request = HealthcareBundleUpdateRequest(
            name = "Pacoti",
        )

        val healthcareBundleToUpdate = healthcareBundle.copy(
            name = request.name!!,
        )

        authenticatedAs(idToken, staff) {
            coEvery {
                healthcareBundleService.get(healthcareBundle.id)
            } returns healthcareBundle.success()

            coEvery {
                healthcareBundleService.update(healthcareBundleToUpdate)
            } returns healthcareBundleToUpdate.success()

            put(to = "/healthcare_bundle/${healthcareBundle.id}", body = request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(healthcareBundleToUpdate)
            }

            coVerifyOnce { healthcareBundleService.get(any()) }
            coVerifyOnce { healthcareBundleService.update(any()) }
        }
    }

    @Test
    fun `#update should clean validBefore if the healthcare_resource has it set up and the request is sending a pending_integration status, meaning it is reactivate operation`() = runBlocking {
        val request = HealthcareBundleUpdateRequest(
            status = HealthcareBundleStatus.PENDING_INTEGRATION
        )

        val healthcareBundle = healthcareBundle.copy(
            status = HealthcareBundleStatus.ACTIVE,
            validBefore = LocalDate.now()
        )

        val healthcareBundleToUpdate = healthcareBundle.copy(
            status = HealthcareBundleStatus.PENDING_INTEGRATION,
            validBefore = null
        )

        authenticatedAs(idToken, staff) {
            coEvery {
                healthcareBundleService.get(healthcareBundle.id)
            } returns healthcareBundle.success()

            coEvery {
                healthcareBundleService.update(match {
                    it.status == HealthcareBundleStatus.PENDING_INTEGRATION &&
                            it.validBefore == null
                })
            } returns healthcareBundleToUpdate.success()

            put(to = "/healthcare_bundle/${healthcareBundle.id}", body = request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(healthcareBundleToUpdate)
            }

            coVerifyOnce { healthcareBundleService.get(any()) }
            coVerifyOnce { healthcareBundleService.update(any()) }
        }
    }

    @Test
    fun `#update should get error when status is PENDING_INTEGRATION`() = runBlocking {
        val request = HealthcareBundleUpdateRequest(
            name = "Pacoti",
            code = "codigo",
        )

        authenticatedAs(idToken, staff) {
            coEvery {
                healthcareBundleService.get(healthcareBundle.id)
            } returns healthcareBundle.copy(status = HealthcareBundleStatus.PENDING_INTEGRATION).success()

            put(to = "/healthcare_bundle/${healthcareBundle.id}", body = request) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }

            coVerifyOnce { healthcareBundleService.get(any()) }
            coVerifyNone { healthcareBundleService.update(any()) }
        }
    }

    @Test
    fun `#update should get error when status is ERROR_INTEGRATION`() = runBlocking {
        val request = HealthcareBundleUpdateRequest(
            name = "Pacoti",
            code = "codigo",
        )

        authenticatedAs(idToken, staff) {
            coEvery {
                healthcareBundleService.get(healthcareBundle.id)
            } returns healthcareBundle.copy(status = HealthcareBundleStatus.ERROR_INTEGRATION).success()

            put(to = "/healthcare_bundle/${healthcareBundle.id}", body = request) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }

            coVerifyOnce { healthcareBundleService.get(any()) }
            coVerifyNone { healthcareBundleService.update(any()) }
        }
    }

    @Test
    fun `#update should get error when hasMedicalFees=true and primaryTuss=null`() = runBlocking {
        val request = HealthcareBundleUpdateRequest(
            name = "Pacoti",
            code = "codigo",
            hasMedicalFees = true,
            primaryTuss = null
        )

        authenticatedAs(idToken, staff) {
            coEvery {
                healthcareBundleService.get(healthcareBundle.id)
            } returns healthcareBundle.copy(primaryTuss = null, hasMedicalFees = false).success()

            put(to = "/healthcare_bundle/${healthcareBundle.id}", body = request) { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }

            coVerifyOnce { healthcareBundleService.get(any()) }
            coVerifyNone { healthcareBundleService.update(any()) }
        }
    }

    @Test
    fun `#get healthcare bundle should return healthcare bundles with service type`() = runBlocking {
        authenticatedAs(idToken, staff) {
            coEvery {
                healthcareBundleService.list(
                    status = listOf(HealthcareBundleStatus.DRAFT, HealthcareBundleStatus.INACTIVE),
                    range = IntRange(0, 49),
                    healthcareBundleListFilters = HealthcareBundleListFilters(
                        searchTerm = "Apendicectomia",
                    )
                )
            } returns HealthcareBundleListWithCount(
                list = listOf(healthcareBundle.copy()),
                count = 1
            ).success()


            get(
                "/healthcare_bundle?filter=" +
                        "{providerUnitId=\"${providerUnit.id}\"," +
                        "q=\"Apendicectomia\"," +
                        "staffId=\"${staff.id}\"," +
                        "serviceTypeId=\"${serviceTypeId}\"}" +
                        "&status=[\"DRAFT\", \"INACTIVE\"]" +
                        "&range=[0,49]"
            ) { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    HealthcareBundleListResponse(
                        listOf(
                            HealthcareBundleResponse(
                                id = healthcareBundle.id,
                                name = healthcareBundle.name,
                                code = healthcareBundle.code,
                                primaryTuss = healthcareBundle.primaryTuss!!,
                                status = healthcareBundle.status,
                                category = healthcareBundle.category,
                                providerName = healthcareBundle.providerName!!,
                                hasMedicalFees = healthcareBundle.hasMedicalFees,
                                providerUnitGroupId = healthcareBundle.providerUnitGroupId
                            )
                        )
                    )
                )
                val content: HealthcareBundleListResponse = response.bodyAsJson()
                Assertions.assertThat(content.healthcareBundles.size).isEqualTo(1)
                Assertions.assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }

            coVerifyOnce {
                healthcareBundleService.list(
                    any(),
                    any(),
                    any(),
                )
            }
        }
    }

    @Test
    fun `#listProviderName should return provider Response`() = runBlocking {
        authenticatedAs(idToken, staff) {
            val healthcareBundle = healthcareBundle.copy(providerName = "Fleury")
            coEvery {
                healthcareBundleService.listProviderName(
                    range = IntRange(0, 49),
                    searchTerm = "Apendicectomia",
                    category = HealthcareBundleCategory.HEALTH_INSTITUTION
                )
            } returns listOf(healthcareBundle).success()

            get(
                "/healthcare_bundle/provider_name?filter=" +
                        "{q=\"Apendicectomia\"," +
                        "category=\"HEALTH_INSTITUTION\"}" +
                        "&range=[0,49]"
            ) { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    ProvidersListResponse(
                        listOf(
                            ProviderResponse(
                                name = healthcareBundle.providerName,
                                category = healthcareBundle.category,
                                id = healthcareBundle.providerUnitGroupId
                            )
                        )
                    )
                )
            }

            coVerifyOnce { healthcareBundleService.listProviderName(any(), any(), any()) }
        }
    }

    @Test
    fun `#get should get healthcare bundle by id`() = runBlocking {
        coEvery {
            healthcareBundleService.get(healthcareBundle.id)
        } returns healthcareBundle.success()
        coEvery {
            healthcareResourceService.get(healthcareResource.id)
        } returns healthcareResource.success()

        val healthcareBundleResponse = HealthcareBundleResponse(
            id = healthcareBundle.id,
            name = healthcareBundle.name,
            code = healthcareBundle.code,
            primaryTuss = healthcareBundle.primaryTuss!!,
            status = healthcareBundle.status,
            category = healthcareBundle.category,
            providerName = healthcareBundle.providerName!!,
            primaryTussDescription = healthcareResource.description,
            hasMedicalFees = healthcareBundle.hasMedicalFees,
            primaryTussId = healthcareResource.id,
            providerUnitGroupId = healthcareBundle.providerUnitGroupId
        )

        authenticatedAs(idToken, staff) {
            get("/healthcare_bundle/${healthcareBundle.id}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(healthcareBundleResponse)
            }
        }

        coVerifyOnce { healthcareBundleService.get(any()) }
    }

    @Test
    fun `#getResources should get healthcare bundle resources by id`() = runBlocking {
        val includedResources = listOf(
            TestModelFactory.buildHealthcareResource(),
            TestModelFactory.buildHealthcareResource()
        )
        val includedResourcesIds = includedResources.map { it.id }

        val excludedResources = listOf(
            TestModelFactory.buildHealthcareResource(),
            TestModelFactory.buildHealthcareResource()
        )
        val excludedResourcesIds = excludedResources.map { it.id }


        val healthcareBundleWithResources = healthcareBundle.copy(
            includedResources = includedResourcesIds,
            excludedResources = excludedResourcesIds,
        )

        coEvery {
            healthcareBundleService.get(healthcareBundleWithResources.id)
        } returns healthcareBundleWithResources.success()

        coEvery {
            healthcareResourceService.findByList(includedResourcesIds.plus(excludedResourcesIds))
        } returns includedResources.plus(excludedResources).success()

        authenticatedAs(idToken, staff) {
            get("/healthcare_bundle/${healthcareBundleWithResources.id}/resources") { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    HealthcareBundleResourcesResponse(
                        includedResources = includedResources,
                        excludedResources = excludedResources
                    )
                )
            }
        }

        coVerifyOnce { healthcareBundleService.get(any()) }
        coVerifyOnce { healthcareResourceService.findByList(any()) }
    }

    @Test
    fun `#getResources should get healthcare bundle resources by id with empty list`() = runBlocking {

        coEvery {
            healthcareBundleService.get(healthcareBundle.id)
        } returns healthcareBundle.success()

        authenticatedAs(idToken, staff) {
            get("/healthcare_bundle/${healthcareBundle.id}/resources") { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    HealthcareBundleResourcesResponse(
                        includedResources = emptyList(),
                        excludedResources = emptyList(),
                    )
                )
            }
        }

        coVerifyOnce { healthcareBundleService.get(any()) }
        coVerifyNone { healthcareResourceService.findByList(any()) }
    }

    @Test
    fun `#getGroups should get healthcare bundle groups by id`() = runBlocking {
        val groups = listOf(
            TestModelFactory.buildHealthcareResourceGroup(),
            TestModelFactory.buildHealthcareResourceGroup()
        )
        val groupsIds = groups.map { it.id }

        val healthcareBundleWithGroups = healthcareBundle.copy(
            groups = groupsIds,
        )

        coEvery {
            healthcareBundleService.get(healthcareBundleWithGroups.id)
        } returns healthcareBundleWithGroups.success()

        coEvery {
            healthcareResourceGroupService.findByList(groupsIds)
        } returns groups.success()

        authenticatedAs(idToken, staff) {
            get("/healthcare_bundle/${healthcareBundle.id}/groups") { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    HealthcareBundleGroupsResponse(groups = groups)
                )
            }
        }

        coVerifyOnce { healthcareBundleService.get(any()) }
        coVerifyOnce { healthcareResourceGroupService.findByList(any()) }
    }

    @Test
    fun `#getGroups should get healthcare bundle groups by id with empty list`() = runBlocking {
        coEvery {
            healthcareBundleService.get(healthcareBundle.id)
        } returns healthcareBundle.success()

        authenticatedAs(idToken, staff) {
            get("/healthcare_bundle/${healthcareBundle.id}/groups") { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    HealthcareBundleGroupsResponse(groups = emptyList())
                )
            }
        }

        coVerifyOnce { healthcareBundleService.get(any()) }
        coVerifyNone { healthcareResourceGroupService.findByList(any()) }
    }

    @Test
    fun `#getAssociatedResource should get associated healthcare resource by id`() = runBlocking {
        coEvery {
            healthcareBundleService.get(healthcareBundle.id)
        } returns healthcareBundle.copy(compositionHash = "hash").success()

        coEvery {
            healthcareResourceService.findByCompositionHash("hash")
        } returns healthcareResource.success()

        authenticatedAs(idToken, staff) {
            get("/healthcare_bundle/${healthcareBundle.id}/associated_resource") { response ->
                ResponseAssert.assertThat(response).isOKWithData(AssociatedResourceResponse(
                    healthcareResource
                ))
            }
        }
    }

    @Test
    fun `#getAssociatedResource should get error healthcare bundle does not have compositionHash`() = runBlocking {
        coEvery {
            healthcareBundleService.get(healthcareBundle.id)
        } returns healthcareBundle.copy(compositionHash = "hash").success()

        coEvery {
            healthcareResourceService.findByCompositionHash("hash")
        } returns NotFoundException("").failure()

        authenticatedAs(idToken, staff) {
            get("/healthcare_bundle/${healthcareBundle.id}/associated_resource") { response ->
                ResponseAssert.assertThat(response).isOKWithData(
                    AssociatedResourceResponse(
                        null
                    )
                )
            }
        }
    }

    @Test
    fun `#delete should delete healthcare bundle if status is DRAFT`() = runBlocking {
        val draftBundle = healthcareBundle.copy(status = HealthcareBundleStatus.DRAFT)

        coEvery {
            healthcareBundleService.get(healthcareBundle.id)
        } returns draftBundle.success()
        coEvery {
            healthcareBundleService.delete(draftBundle)
        } returns true.success()

        authenticatedAs(idToken, staff) {
            delete("/healthcare_bundle/${healthcareBundle.id}") { response ->
                ResponseAssert.assertThat(response).isOKWithData(true)
            }
        }

        coVerifyOnce { healthcareBundleService.get(any()) }
        coVerifyOnce { healthcareBundleService.delete(any()) }
    }

    @Test
    fun `#delete should not delete healthcare bundle if status is not DRAFT`() = runBlocking {
        coEvery {
            healthcareBundleService.get(healthcareBundle.id)
        } returns healthcareBundle.copy(status = HealthcareBundleStatus.ACTIVE).success()

        authenticatedAs(idToken, staff) {
            delete("/healthcare_bundle/${healthcareBundle.id}") { response ->
                ResponseAssert.assertThat(response).isBadRequest()
            }
        }

        coVerifyOnce { healthcareBundleService.get(any()) }
        coVerifyNone { healthcareBundleService.delete(any()) }
    }

    @Test
    fun `#updateStatus should not updateStatus of healthcare bundles to pending integration when there are no healthcare resources associated`() = runBlocking {
        val request = HealthcareBundleUpdateStatusListRequest(
            ids = healthcareBundles.map { it.id },
            status = HealthcareBundleStatus.PENDING_INTEGRATION
        )

        coEvery {
            healthcareBundleService.getByIds(healthcareBundles.map { it.id })
        } returns healthcareBundles.success()

        coEvery {
            healthcareResourceService.findByCompositionHashList(healthcareBundles.mapNotNull { it.compositionHash })
        } returns emptyList<HealthcareResource>().success()

        val expectedResponse = healthcareBundles

        authenticatedAs(idToken, staff) {
            put(to = "/healthcare_bundles/status", body = request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthcareBundleService.getByIds(any()) }
        coVerifyNone { healthcareBundleService.updateMany(any()) }
    }

    @Test
    fun `#updateStatus should updateStatus of healthcare bundles to pending integration when there are healthcare resources associated`() = runBlocking {
        val request = HealthcareBundleUpdateStatusListRequest(
            ids = healthcareBundles.map { it.id },
            status = HealthcareBundleStatus.PENDING_INTEGRATION
        )

        val healthcareResource = TestModelFactory.buildHealthcareResource(
            compositionHash = healthcareBundles.first().compositionHash
        )

        coEvery {
            healthcareBundleService.getByIds(healthcareBundles.map { it.id })
        } returns healthcareBundles.success()

        coEvery {
            healthcareResourceService.findByCompositionHashList(healthcareBundles.mapNotNull { it.compositionHash })
        } returns listOf(healthcareResource).success()

        val expectedResponse = healthcareBundles.map { it.copy(status = HealthcareBundleStatus.PENDING_INTEGRATION) }

        coEvery {
            healthcareBundleService.updateMany(match {
                it.size == 5 &&
                        it.first().status == HealthcareBundleStatus.PENDING_INTEGRATION &&
                        it.last().status == HealthcareBundleStatus.PENDING_INTEGRATION
            })
        } returns expectedResponse.success()

        authenticatedAs(idToken, staff) {
            put(to = "/healthcare_bundles/status", body = request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthcareBundleService.getByIds(any()) }
        coVerifyOnce { healthcareBundleService.updateMany(any()) }
    }

    @Test
    fun `#updateStatus should updateStatus of healthcare bundles to pending integration only when there are healthcare resources associated`() = runBlocking {
        val healthcareBundles = healthcareBundles.plus(healthcareBundles.first().copy(compositionHash = "380912839"))

        val request = HealthcareBundleUpdateStatusListRequest(
            ids = healthcareBundles.map { it.id },
            status = HealthcareBundleStatus.PENDING_INTEGRATION
        )

        val healthcareResource = TestModelFactory.buildHealthcareResource(
            compositionHash = healthcareBundles.first().compositionHash
        )

        coEvery {
            healthcareBundleService.getByIds(healthcareBundles.map { it.id })
        } returns healthcareBundles.success()

        coEvery {
            healthcareResourceService.findByCompositionHashList(healthcareBundles.mapNotNull { it.compositionHash })
        } returns listOf(healthcareResource).success()

        val expectedResponse = healthcareBundles.map { it.copy(status = HealthcareBundleStatus.PENDING_INTEGRATION) }

        coEvery {
            healthcareBundleService.updateMany(match {
                it.size == 5 &&
                        it.first().status == HealthcareBundleStatus.PENDING_INTEGRATION &&
                        it.last().status == HealthcareBundleStatus.PENDING_INTEGRATION
            })
        } returns expectedResponse.success()

        authenticatedAs(idToken, staff) {
            put(to = "/healthcare_bundles/status", body = request) { response ->
                ResponseAssert.assertThat(response).isOKWithData(expectedResponse)
            }
        }

        coVerifyOnce { healthcareBundleService.getByIds(any()) }
        coVerifyOnce { healthcareBundleService.updateMany(any()) }
    }

}
