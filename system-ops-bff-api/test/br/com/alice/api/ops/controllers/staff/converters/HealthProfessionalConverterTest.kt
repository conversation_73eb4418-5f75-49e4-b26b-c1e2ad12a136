package br.com.alice.api.ops.controllers.staff.converters

import br.com.alice.api.ops.controllers.staff.models.CouncilDTO
import br.com.alice.api.ops.controllers.staff.models.UserRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.core.StaffType
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.Gender
import br.com.alice.common.models.SpecialistTier
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Council
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.SpecialistStatus
import br.com.alice.data.layer.models.HealthSpecialistScoreEnum
import br.com.alice.data.layer.models.Qualification
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.Test

class HealthProfessionalConverterTest {

    @Test
    fun `convert should convert health professional`() {
        val contact = TestModelFactory.buildContact()
        val contactDto = ContactModelConverter.convert(contact)
        val healthProfessionalId = RangeUUID.generate()
        val staffId = RangeUUID.generate()
        val user = UserRequest(
            "firstName",
            "lastName",
            "email",
            "nationalId",
            LocalDate.now(),
            Gender.MALE,
            "profileImageUrl",
            Role.HEALTH_OPS,
            StaffType.HEALTH_PROFESSIONAL,
            true,
            "urlSlug",
            "quote",
            "profileBio",
            CouncilDTO(number = "number", state = State.SP, type = 1),
            RangeUUID.generate(),
            listOf(RangeUUID.generate()),
            RangeUUID.generate(),
            listOf(RangeUUID.generate()),
            listOf(RangeUUID.generate()),
            listOf(Qualification.ISO_9001),
            SpecialistTier.EXPERT,
            SpecialistTier.EXPERT,
            "curiosity",
            true,
            listOf("education"),
            HealthSpecialistScoreEnum.DOMINATING,
            LocalDate.now(),
            listOf(contactDto),
            0,
            onVacationStart = LocalDateTime.of(2025, 1, 1, 20, 0).toLocalDate(),
            onVacationUntil = LocalDateTime.of(2025, 1, 10, 20, 0).toLocalDate(),
        )

        val expected = HealthProfessional(
            id = healthProfessionalId,
            name = "firstName lastName",
            status = SpecialistStatus.ACTIVE,
            staffId = staffId,
            specialtyId = user.specialty,
            subSpecialtyIds = user.subSpecialties.orEmpty(),
            imageUrl = user.profileImageUrl,
            gender = user.gender,
            internalSpecialtyId = user.internalSpecialty,
            internalSubSpecialtyIds = user.internalSubSpecialties.orEmpty(),
            providerUnitIds = user.providerUnits.orEmpty(),
            showOnApp = user.showOnApp ?: false,
            contacts = listOf(contact),
            council = Council(number = "number", state = State.SP, type = CouncilType.CRAS),
            qualifications = user.qualifications.orEmpty(),
            tier = user.tier,
            theoristTier = user.theoristTier,
            curiosity = user.curiosity,
            education = user.education.orEmpty(),
            healthSpecialistScore = user.healthSpecialistScore,
            deAccreditationDate = user.deAccreditationDate,
            profileBio = user.profileBio,
            quote = user.quote,
            paymentFrequency = user.paymentFrequency,
            email = user.email,
            nationalId = user.nationalId,
            type = user.type,
            role = user.role,
            urlSlug = user.urlSlug,
            onVacationStart = LocalDate.of(2025, 1, 1).atTime(12, 0),
            onVacationUntil = LocalDate.of(2025, 1, 10).atTime(12, 0),
            version = 0
        )

        val result = HealthProfessionalConverter.convert(user, healthProfessionalId, staffId)
        assertThat(result)
            .usingRecursiveComparison()
            .ignoringFields("createdAt", "updatedAt", "contacts.updatedAt", "contacts.createdAt")
            .isEqualTo(expected)
    }

}
