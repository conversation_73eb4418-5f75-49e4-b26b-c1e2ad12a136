package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AttentionLevel
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.MedicalSpecialty
import br.com.alice.data.layer.models.MedicalSpecialtyType.SPECIALTY
import br.com.alice.data.layer.models.MedicalSpecialtyType.SUBSPECIALTY
import br.com.alice.provider.client.CboCodeService
import br.com.alice.provider.client.MedicalSpecialtyService
import br.com.alice.staff.client.HealthProfessionalService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class MedicalSpecialtyControllerTest : ControllerTestHelper() {

    private val medicalSpecialtyService: MedicalSpecialtyService = mockk()
    private val healthProfessionalService: HealthProfessionalService = mockk()
    private val cboCodeService: CboCodeService = mockk()

    private val controller = MedicalSpecialtyController(
        medicalSpecialtyService,
        healthProfessionalService,
        cboCodeService
    )

    private val specialty = TestModelFactory.buildMedicalSpecialty(cboCode = null)
    private val subSpecialty =
        TestModelFactory.buildMedicalSpecialty(name = "Joelho", parentSpecialtyId = specialty.id, type = SUBSPECIALTY)

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(medicalSpecialtyService)
        module.single { controller }
    }

    @Test
    fun `#index should list specialties by range`() {
        coEvery {
            medicalSpecialtyService.getByQuery("test", null, null)
        } returns listOf(specialty).success()


        authenticatedAs(idToken, staff) {
            get("/medicalSpecialty?filter={q='test'}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<MedicalSpecialty> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.first()).isEqualTo(specialty)
            }
        }
    }

    @Test
    fun `#index should list specialties filtered by name`() {
        val name = "xxxx"
        coEvery { medicalSpecialtyService.getByQuery(name, null,null) } returns listOf(specialty).success()

        authenticatedAs(idToken, staff) {
            get("/medicalSpecialty?filter={\"q\":\"${name}\"}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<MedicalSpecialty> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content).isEqualTo(listOf(specialty))
            }
        }
    }

    @Test
    fun `#getById should get specialty`() {
        coEvery { medicalSpecialtyService.getById(specialty.id) } returns specialty.success()

        authenticatedAs(idToken, staff) {
            get("/medicalSpecialty/${specialty.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: MedicalSpecialty = response.bodyAsJson()
                assertThat(content).isEqualTo(specialty)
            }
        }
    }

    @Test
    fun `#getSpecialty should list Providers by name filter`() {
        coEvery { medicalSpecialtyService.getByName("ortopedia", SPECIALTY) } returns listOf(specialty).success()

        authenticatedAs(idToken, staff) {
            get("/specialties?filter={\"q\":\"ortopedia\"}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<MedicalSpecialty> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.first()).isEqualTo(specialty)
            }
        }
    }

    @Test
    fun `#getSpecialty should list specialties by range`() {
        coEvery { medicalSpecialtyService.getByRange(IntRange(0, 49), listOf(SPECIALTY)) } returns listOf(specialty).success()

        coEvery { medicalSpecialtyService.count() } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/specialties?filter={}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<MedicalSpecialty> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.first()).isEqualTo(specialty)
            }
        }
    }

    @Test
    fun `#getSubSpecialty should list Providers by name filter`() {
        coEvery { medicalSpecialtyService.getByName("ortopedia", SUBSPECIALTY) } returns listOf(specialty).success()

        authenticatedAs(idToken, staff) {
            get("/sub_specialties?filter={\"q\":\"ortopedia\"}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<MedicalSpecialty> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.first()).isEqualTo(specialty)
            }
        }
    }

    @Test
    fun `#getSubSpecialty should list specialties by range`() {
        coEvery {
            medicalSpecialtyService.getByRange(
                IntRange(0, 49),
                listOf(SUBSPECIALTY)
            )
        } returns listOf(specialty).success()

        coEvery { medicalSpecialtyService.count() } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/sub_specialties?filter={}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<MedicalSpecialty> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.first()).isEqualTo(specialty)
            }
        }
    }

    @Test
    fun `#getSubSpecialty should list specialties by parent id`() {
        coEvery { medicalSpecialtyService.getByParentId(specialty.id) } returns listOf(subSpecialty).success()

        coEvery { medicalSpecialtyService.count() } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/sub_specialties?filter={\"parent_specialty_id\":\"${specialty.id}\"}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<MedicalSpecialty> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.first()).isEqualTo(subSpecialty)
            }
        }
    }

    @Test
    fun `#getSubSpecialty should list sub specialties by ids`() {
        coEvery { medicalSpecialtyService.getByIds(listOf(subSpecialty.id)) } returns listOf(subSpecialty).success()

        authenticatedAs(idToken, staff) {
            get("/sub_specialties?filter={\"id\":[\"${subSpecialty.id}\"]}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<MedicalSpecialty> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                assertThat(content.first()).isEqualTo(subSpecialty)
            }
        }
    }

    @Test
    fun `#create should create specialty`() {
        val request = MedicalSpecialtyRequest(
            name = "ortopedia",
            type = SPECIALTY,
            urlSlug = "ortopedista",
            generateGeneralistSubSpecialty = true,
            attentionLevel = AttentionLevel.SECONDARY,
            isTherapy = true
        )
        val expectedSpecialty = specialty.copy(
            id = RangeUUID.generate(), urlSlug = "ortopedista", generateGeneralistSubSpecialty = true
        )
        val expectedSubSpecialty = MedicalSpecialty(
            "Generalista", SUBSPECIALTY, parentSpecialtyId = expectedSpecialty.id, urlSlug = "", id = RangeUUID.generate()
        )

        coEvery {
            medicalSpecialtyService.add(match { it.type == SPECIALTY })
        } returns expectedSpecialty.success()
        coEvery {
            medicalSpecialtyService.add(match { it.type == SUBSPECIALTY })
        } returns expectedSubSpecialty.success()

        authenticatedAs(idToken, staff) {
            post("/medicalSpecialty", request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: MedicalSpecialty = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedSpecialty)

                coVerify {
                    medicalSpecialtyService.add(match {
                        it.name == request.name
                                && it.type == SPECIALTY
                                && it.parentSpecialtyId == null
                                && it.urlSlug == request.urlSlug
                                && it.attentionLevel == request.attentionLevel
                                && it.isTherapy == request.isTherapy
                    })
                    medicalSpecialtyService.add(match {
                        it.name == "Generalista"
                                && it.type == SUBSPECIALTY
                                && it.parentSpecialtyId == expectedSpecialty.id
                    })
                }
            }
        }
    }

    @Test
    fun `#create should create specialty don't should return error when subSpecialty is not created`() {
        val request = MedicalSpecialtyRequest(
            name = "ortopedia",
            type = SPECIALTY,
            urlSlug = "ortopedista",
            generateGeneralistSubSpecialty = true
        )
        val expectedSpecialty = specialty.copy(
            id = RangeUUID.generate(), urlSlug = "ortopedista", generateGeneralistSubSpecialty = true
        )

        coEvery {
            medicalSpecialtyService.add(match { it.type == SPECIALTY })
        } returns expectedSpecialty.success()
        coEvery {
            medicalSpecialtyService.add(match { it.type == SUBSPECIALTY })
        } returns NotFoundException("Not found").failure()

        authenticatedAs(idToken, staff) {
            post("/medicalSpecialty", request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: MedicalSpecialty = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedSpecialty)

                coVerify {
                    medicalSpecialtyService.add(match {
                        it.name == request.name
                                && it.type == SPECIALTY
                                && it.parentSpecialtyId == null
                                && it.urlSlug == request.urlSlug
                    })
                    medicalSpecialtyService.add(match {
                        it.name == "Generalista"
                                && it.type == SUBSPECIALTY
                                && it.parentSpecialtyId == expectedSpecialty.id
                    })
                }
            }
        }
    }

    @Test
    fun `#create should create subSpecialty`() {
        val request = MedicalSpecialtyRequest(
            name = "ortopedia",
            type = SUBSPECIALTY,
            urlSlug = "ortopedista",
            parentSpecialtyId = RangeUUID.generate(),
            generateGeneralistSubSpecialty = false
        )
        val expectedSubSpecialty = specialty.copy(
            id = RangeUUID.generate(), urlSlug = "ortopedista"
        )

        coEvery {
            medicalSpecialtyService.add(match { it.type == SUBSPECIALTY })
        } returns expectedSubSpecialty.success()

        authenticatedAs(idToken, staff) {
            post("/medicalSpecialty", request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: MedicalSpecialty = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedSubSpecialty)

                coVerify {
                    medicalSpecialtyService.add(match {
                        it.name == request.name && it.type == SUBSPECIALTY
                    })
                }
            }
        }
    }

    @Test
    fun `#update should update specialty`() {
        val request = MedicalSpecialtyRequest(
            name = "ortopedia",
            type = SPECIALTY,
            urlSlug = "ortopedia",
            generateGeneralistSubSpecialty = false,
            attentionLevel = AttentionLevel.PRIMARY,
            isTherapy = true
        )
        val toUpdate = specialty.copy(
            name = request.name,
            type = request.type,
            attentionLevel = request.attentionLevel,
            isTherapy = request.isTherapy
        )

        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns specialty.success()

        coEvery {
            medicalSpecialtyService.update(toUpdate)
        } returns toUpdate.success()

        coEvery {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(
                listOf(specialty.id)
            )
        } returns emptyList<HealthProfessional>().success()

        authenticatedAs(idToken, staff) {
            put("/medicalSpecialty/${specialty.id}", request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: MedicalSpecialty = response.bodyAsJson()
                assertThat(content).isEqualTo(toUpdate)
            }
        }
    }

    @Test
    fun `#update returns an error if deactivating a specialty with a specialist associated`() {
        val request = MedicalSpecialtyRequest(
            name = "ortopedia",
            type = SPECIALTY,
            active = false,
            urlSlug = "ortopedia",
            generateGeneralistSubSpecialty = false
        )
        val specialist = TestModelFactory.buildHealthProfessional()

        coEvery {
            medicalSpecialtyService.getById(specialty.id)
        } returns specialty.success()

        coEvery {
            healthProfessionalService.findActivesByInternalAndExternalSpecialties(
                listOf(specialty.id)
            )
        } returns listOf(specialist).success()

        authenticatedAs(idToken, staff) {
            put("/medicalSpecialty/${specialty.id}", request) { response ->
                assertThat(response).isBadRequestWithErrorCode(
                    "invalid_specialty_state",
                )
            }
        }

        coVerify(exactly = 1) { medicalSpecialtyService.getById(any()) }
        coVerify(exactly = 1) { healthProfessionalService.findActivesByInternalAndExternalSpecialties(any()) }
        coVerify(exactly = 0) { medicalSpecialtyService.update(any()) }
    }

    @Test
    fun `#create should create advanced access subSpecialty`() {
        val request = MedicalSpecialtyRequest(
            name = "ortopedia",
            type = SUBSPECIALTY,
            urlSlug = "ortopedista",
            parentSpecialtyId = RangeUUID.generate(),
            generateGeneralistSubSpecialty = false,
            isAdvancedAccess = true
        )
        val expectedSubSpecialty = specialty.copy(
            id = RangeUUID.generate(), urlSlug = "ortopedista"
        )

        coEvery {
            medicalSpecialtyService.add(match { it.type == SUBSPECIALTY })
        } returns expectedSubSpecialty.success()

        authenticatedAs(idToken, staff) {
            post("/medicalSpecialty", request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: MedicalSpecialty = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedSubSpecialty)

                coVerifyOnce {
                    medicalSpecialtyService.add(match {
                        it.name == request.name && it.type == SUBSPECIALTY
                    })
                }
            }
        }
    }
}
