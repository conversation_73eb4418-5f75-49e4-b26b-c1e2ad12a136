package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.converters.ProviderUnitConverter
import br.com.alice.api.ops.converters.ProviderUnitResponseConverter
import br.com.alice.api.ops.models.InputList
import br.com.alice.api.ops.models.LivanceIntegrationTransport
import br.com.alice.api.ops.models.ProviderUnitRequest
import br.com.alice.api.ops.models.ProviderUnitResponse
import br.com.alice.api.ops.models.ProviderUnitWithAdditionalInfoResponse
import br.com.alice.api.ops.utils.BankInstitutions
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockRangeUuidAndDateTime
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.MedicalSpecialtyProfile
import br.com.alice.data.layer.models.PartnerIntegrationProviderType
import br.com.alice.data.layer.models.PartnerIntegrationProviderUnit
import br.com.alice.data.layer.models.ProviderUnit
import br.com.alice.data.layer.models.ProviderUnitGroup
import br.com.alice.data.layer.models.StructuredAddress
import br.com.alice.data.layer.models.StructuredAddressReferenceModel
import br.com.alice.provider.client.PartnerIntegrationProviderUnitService
import br.com.alice.provider.client.ProviderService
import br.com.alice.provider.client.ProviderUnitFilter
import br.com.alice.provider.client.ProviderUnitGroupService
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class ProviderUnitControllerTest : ControllerTestHelper() {

    private val providerUnitService: ProviderUnitService = mockk()
    private val providerService: ProviderService = mockk()
    private val providerUnitGroupService: ProviderUnitGroupService = mockk()
    private val partnerIntegrationService: PartnerIntegrationProviderUnitService = mockk()

    private val controller = ProviderUnitController(
        providerUnitService,
        providerUnitGroupService,
        partnerIntegrationService
    )

    private val providerId = RangeUUID.generate()
    private val medicalSpecialtyProfileList =
        listOf(MedicalSpecialtyProfile(RangeUUID.generate()), MedicalSpecialtyProfile(RangeUUID.generate()))
    private val providerUnitGroupId = RangeUUID.generate()
    private val providerUnitGroup = ProviderUnitGroup(id = providerUnitGroupId, title = "Oswaldo Cruz")
    private val providerUnit = TestModelFactory.buildProviderUnit(
        providerId = providerId,
        name = providerUnitGroup.title,
        cnes = "********",
        bankCode = "341",
        accountNumber = "01212-3",
        agencyNumber = "1720",
        medicalSpecialtyProfile = medicalSpecialtyProfileList,
        urlSlug = "oswaldo-cruz-paulista",
    )
    private val city = "Sao Paulo"
    private val state = "SP"
    private val latitude = "-23.5718116"
    private val longitude = "-46.**************"

    private val expectedResponseWithoutAddress = ProviderUnitResponseConverter.convert(
        ProviderUnitWithAdditionalInfoResponse(
            providerUnit = providerUnit,
        )
    )

    private val address = TestModelFactory.buildStructuredAddress(
        referencedModelId = providerId,
        referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
        city = city,
        state = state,
        latitude = latitude,
        longitude = longitude,
    )
    private val expectedResponse = ProviderUnitResponseConverter.convert(
        ProviderUnitWithAdditionalInfoResponse(
            providerUnit = providerUnit,
            address = address
        )
    )
    private val expectedEmptyInputList = emptyList<InputList>()
    private val expectedProviderUnitGroupInputList =
        listOf(InputList(providerUnitGroup.id.toString(), providerUnitGroup.title))

    private val request = ProviderUnitRequest(
        type = ProviderUnit.Type.HOSPITAL,
        name = providerUnitGroup.title,
        providerUnitGroupId = providerUnitGroupId,
        site = null,
        cnpj = null,
        imageUrl = null,
        phones = emptyList(),
        workingPeriods = emptyList(),
        qualifications = emptyList(),
        products = emptyList(),
        providerId = providerId,
        street = "R. Treze de Maio",
        number = "1815",
        complement = "",
        neighborhood = "Bela Vista",
        state = "SP",
        city = city,
        latitude = latitude,
        longitude = longitude,
        zipcode = "01323-020",
        cnes = "********",
        bankCode = "341",
        accountNumber = "01212-3",
        agencyNumber = "1720",
        urlSlug = "oswaldo-cruz-paulista",
        medicalSpecialtyProfile = medicalSpecialtyProfileList,
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(
            providerUnitService,
            providerService,
        )
        module.single { controller }
    }

    private val defaultRange = IntRange(0, 19)

    @Test
    fun `#index should list ProviderUnits by name and type filter`() {
        val filter = ProviderUnitFilter(
            searchToken = "oswaldo",
            type = listOf(ProviderUnit.Type.EMERGENCY_UNITY)
        )
        coEvery {
            providerUnitService.getByFilterWithRange(filter, defaultRange)
        } returns listOf(providerUnit).success()
        coEvery { providerUnitService.countByFilter(filter) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/v2/provider_units?filter={\"q\":\"oswaldo\", \"type\":\"EMERGENCY_UNITY\"}") { response ->
                assertThat(response).isOKWithData(listOf(expectedResponseWithoutAddress))
                assertThat(response).containsHeaderWithValue("Content-Range", "1")
            }
        }
    }

    @Test
    fun `#index should list ProviderUnits by name and types filter`() {
        val filter = ProviderUnitFilter(
            searchToken = "oswaldo",
            type = listOf(ProviderUnit.Type.HOSPITAL, ProviderUnit.Type.MATERNITY)
        )
        coEvery {
            providerUnitService.getByFilterWithRange(
                filter, defaultRange
            )
        } returns listOf(providerUnit).success()
        coEvery { providerUnitService.countByFilter(filter) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/v2/provider_units?filter={\"q\":\"oswaldo\", \"type\":\"HOSPITAL,MATERNITY\"}") { response ->
                assertThat(response).isOKWithData(listOf(expectedResponseWithoutAddress))
                assertThat(response).containsHeaderWithValue("Content-Range", "1")
            }
        }
    }

    @Test
    fun `#index should list ProviderUnits by type filter`() {
        val filter = ProviderUnitFilter(
            type = listOf(ProviderUnit.Type.HOSPITAL)
        )
        coEvery {
            providerUnitService.getByFilterWithRange(
                filter, defaultRange
            )
        } returns listOf(providerUnit, providerUnit).success()
        coEvery { providerUnitService.countByFilter(filter) } returns 2.success()

        authenticatedAs(idToken, staff) {
            get("/v2/provider_units?filter={\"type\":\"HOSPITAL\"}") { response ->
                assertThat(response).isOKWithData(
                    listOf(
                        expectedResponseWithoutAddress,
                        expectedResponseWithoutAddress
                    )
                )
                assertThat(response).containsHeaderWithValue("Content-Range", "2")
            }
        }
    }

    @Test
    fun `#index should list ProviderUnits by status list filter`() {
        val filter = ProviderUnitFilter(
            status = listOf(Status.ACTIVE, Status.INACTIVE)
        )
        coEvery {
            providerUnitService.getByFilterWithRange(
                filter, defaultRange
            )
        } returns listOf(providerUnit, providerUnit).success()
        coEvery { providerUnitService.countByFilter(filter) } returns 2.success()

        authenticatedAs(idToken, staff) {
            get("/v2/provider_units?filter={\"status\":\"ACTIVE,INACTIVE\"}") { response ->
                assertThat(response).isOKWithData(
                    listOf(
                        expectedResponseWithoutAddress,
                        expectedResponseWithoutAddress
                    )
                )
                assertThat(response).containsHeaderWithValue("Content-Range", "2")
            }
        }
    }

    @Test
    fun `#index should list ProviderUnits by status filter`() {
        val filter = ProviderUnitFilter(
            status = listOf(Status.ACTIVE)
        )
        coEvery {
            providerUnitService.getByFilterWithRange(
                filter, defaultRange
            )
        } returns listOf(providerUnit, providerUnit).success()
        coEvery { providerUnitService.countByFilter(filter) } returns 2.success()

        authenticatedAs(idToken, staff) {
            get("/v2/provider_units?filter={\"status\":\"ACTIVE\"}") { response ->
                assertThat(response).isOKWithData(
                    listOf(
                        expectedResponseWithoutAddress,
                        expectedResponseWithoutAddress
                    )
                )
                assertThat(response).containsHeaderWithValue("Content-Range", "2")
            }
        }
    }

    @Test
    fun `#index should list ProviderUnits by ids filter`() {
        val filter = ProviderUnitFilter(
            ids = listOf(providerUnit.providerId)
        )
        coEvery {
            providerUnitService.getByFilterWithRange(
                filter, defaultRange
            )
        } returns listOf(providerUnit).success()
        coEvery { providerUnitService.countByFilter(filter) } returns 1.success()

        authenticatedAs(idToken, staff) {
            //filter={"id":["e1f6f489-9950-4ab3-98c9-74860b2e1700"]}
            get("/v2/provider_units?filter={\"id\":[\"${providerUnit.providerId}\"]}") { response ->
                assertThat(response).isOKWithData(listOf(expectedResponseWithoutAddress))
                assertThat(response).containsHeaderWithValue("Content-Range", "1")
            }
        }
    }

    @Test
    fun `#index should list ProviderUnits by range`() {
        val filter = ProviderUnitFilter()

        coEvery {
            providerUnitService.getByFilterWithRange(
                filter, IntRange(0, 49)
            )
        } returns listOf(providerUnit).success()

        coEvery { providerUnitService.countByFilter(filter) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/v2/provider_units?filter={}&range=[0,49]") { response ->
                assertThat(response).isOKWithData(listOf(expectedResponseWithoutAddress))
                assertThat(response).containsHeaderWithValue("Content-Range", "1")
            }
        }
    }

    @Test
    fun `#getById should get ProviderUnit`() {
        coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.copy(address = address).success()
        coEvery {
            partnerIntegrationService.getByProviderUnitIdAndProviderType(
                providerUnit.id,
                PartnerIntegrationProviderType.LIVANCE
            )
        } returns NotFoundException("not_found").failure()

        authenticatedAs(idToken, staff) {
            get("/v2/provider_units/${providerUnit.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: ProviderUnitResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)

                coVerifyOnce { providerUnitService.get(any()) }
                coVerifyOnce { partnerIntegrationService.getByProviderUnitIdAndProviderType(any(), any()) }
            }
        }
    }

    @Test
    fun `#create should create ProviderUnit`() {
        authenticatedAs(idToken, staff) {
            val localDate = LocalDate.of(2023, 1, 1)
            val request = request.copy(
                medicalSpecialtyProfile = medicalSpecialtyProfileList,
                deAccreditationDate = localDate,
                showOnScheduler = true,
                attendanceTypes = listOf(
                    ProviderUnit.AttendanceType.EMERGENCY_CARE,
                    ProviderUnit.AttendanceType.HOSPITALIZATION
                )
            )
            val providerUnitFromRequest =
                ProviderUnitConverter.convert(request)
            val providerUnitAddress = StructuredAddress(
                street = request.street,
                number = request.number,
                complement = request.complement,
                neighborhood = request.neighborhood,
                city = request.city,
                state = request.state,
                zipcode = request.zipcode,
                latitude = request.latitude,
                longitude = request.longitude
            )
            val expectedResponseFromWrite = ProviderUnitResponseConverter.convert(
                ProviderUnitWithAdditionalInfoResponse(
                    providerUnit = providerUnitFromRequest,
                    address = providerUnitAddress,
                )
            )

            coEvery {
                providerUnitService.addWithAddress(
                    match {
                        it == providerUnitFromRequest.copy(
                            id = it.id,
                            createdAt = it.createdAt,
                            updatedAt = it.updatedAt
                        )
                    },
                    match {
                        it == providerUnitAddress.copy(id = it.id, createdAt = it.createdAt, updatedAt = it.updatedAt)
                    }
                )
            } returns providerUnitFromRequest.copy(
                address = providerUnitAddress.copy(
                    referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
                    referencedModelId = providerUnitFromRequest.id,
                )
            ).success()

            post(to = "/v2/provider_units", body = request) { response ->
                assertThat(response).isOKWithData(expectedResponseFromWrite)

                coVerifyOnce { providerUnitService.addWithAddress(any(), any()) }
                confirmVerified(
                    providerUnitService,
                    providerUnitGroupService,
                )
            }
        }
    }

    @Test
    fun `#create should create ProviderUnit containing livance integration data`() = mockRangeUuidAndDateTime { id, _ ->
        authenticatedAs(idToken, staff) {
            val localDate = LocalDate.of(2023, 1, 1)

            val livanceLocationId = "123"
            val partnerIntegration = PartnerIntegrationProviderUnit(
                providerType = PartnerIntegrationProviderType.LIVANCE,
                providerUnitId = id,
                externalId = livanceLocationId
            )
            val livanceIntegration = LivanceIntegrationTransport(livanceLocationId)

            val request = request.copy(
                medicalSpecialtyProfile = medicalSpecialtyProfileList,
                deAccreditationDate = localDate,
                showOnScheduler = true,
                attendanceTypes = listOf(
                    ProviderUnit.AttendanceType.EMERGENCY_CARE,
                    ProviderUnit.AttendanceType.HOSPITALIZATION
                ),
                livance = livanceIntegration
            )
            val providerUnitFromRequest =
                ProviderUnitConverter.convert(request)
            val providerUnitAddress = StructuredAddress(
                street = request.street,
                number = request.number,
                complement = request.complement,
                neighborhood = request.neighborhood,
                city = request.city,
                state = request.state,
                zipcode = request.zipcode,
                latitude = request.latitude,
                longitude = request.longitude
            )
            val expectedResponseFromWrite = ProviderUnitResponseConverter.convert(
                ProviderUnitWithAdditionalInfoResponse(
                    providerUnit = providerUnitFromRequest,
                    address = providerUnitAddress,
                    livance = livanceIntegration
                )
            )

            coEvery {
                providerUnitService.addWithAddress(providerUnitFromRequest, providerUnitAddress)
            } returns providerUnitFromRequest.copy(
                address = providerUnitAddress.copy(
                    referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
                    referencedModelId = providerUnitFromRequest.id,
                )
            ).success()

            coEvery {
                partnerIntegrationService.create(partnerIntegration)
            } returns partnerIntegration.success()

            post(to = "/v2/provider_units", body = request) { response ->
                assertThat(response).isOKWithData(expectedResponseFromWrite)

                coVerifyOnce { providerUnitService.addWithAddress(any(), any()) }
                coVerifyOnce { partnerIntegrationService.create(any()) }
                confirmVerified(
                    providerUnitService,
                    providerUnitGroupService,
                    partnerIntegrationService
                )
            }
        }
    }

    @Test
    fun `#update should update ProviderUnit`() {
        val request = request.copy(
            medicalSpecialtyProfile = medicalSpecialtyProfileList,
            attendanceTypes = listOf(
                ProviderUnit.AttendanceType.EMERGENCY_CARE
            )
        )
        val providerUnitFromRequest = ProviderUnitConverter.convert(request)
        val providerUnitAddress = StructuredAddress(
            street = request.street,
            number = request.number,
            complement = request.complement,
            neighborhood = request.neighborhood,
            city = request.city,
            state = request.state,
            zipcode = request.zipcode,
            latitude = request.latitude,
            longitude = request.longitude

        )
        val expectedResponseFromWrite = ProviderUnitResponseConverter.convert(
            ProviderUnitWithAdditionalInfoResponse(
                providerUnit = providerUnitFromRequest,
                address = providerUnitAddress,
            )
        )

        coEvery { providerUnitService.get(providerUnitFromRequest.id) } returns providerUnitFromRequest.success()
        coEvery {
            partnerIntegrationService.getByProviderUnitIdAndProviderType(
                providerUnitFromRequest.id,
                PartnerIntegrationProviderType.LIVANCE
            )
        } returns NotFoundException("not_found").failure()
        coEvery {
            providerUnitService.updateWithAddress(
                match {
                    it == providerUnitFromRequest.copy(id = it.id, createdAt = it.createdAt, updatedAt = it.updatedAt)
                },
                match {
                    it == providerUnitAddress.copy(id = it.id, createdAt = it.createdAt, updatedAt = it.updatedAt)
                }
            )
        } returns providerUnitFromRequest.copy(
            address = providerUnitAddress.copy(
                referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
                referencedModelId = providerUnitFromRequest.id,
            )
        ).success()

        authenticatedAs(idToken, staff) {
            put(to = "/v2/provider_units/${providerUnitFromRequest.id}", body = request) { response ->
                assertThat(response).isOKWithData(expectedResponseFromWrite)

                coVerifyOnce { providerUnitService.get(any()) }
                coVerifyOnce { providerUnitService.updateWithAddress(any(), any()) }
                coVerifyOnce { partnerIntegrationService.getByProviderUnitIdAndProviderType(any(), any()) }

                confirmVerified(
                    providerUnitService,
                    providerUnitGroupService,
                    partnerIntegrationService
                )
            }
        }
    }

    @Test
    fun `#update should update ProviderUnit and create new partner integration provider unit`() =
        mockRangeUuidAndDateTime { id, _ ->
            val livanceLocationId = "123"
            val partnerIntegration = PartnerIntegrationProviderUnit(
                providerType = PartnerIntegrationProviderType.LIVANCE,
                providerUnitId = id,
                externalId = livanceLocationId
            )
            val livanceIntegration = LivanceIntegrationTransport(livanceLocationId)
            val request = request.copy(
                medicalSpecialtyProfile = medicalSpecialtyProfileList,
                attendanceTypes = listOf(
                    ProviderUnit.AttendanceType.EMERGENCY_CARE
                ),
                livance = livanceIntegration
            )
            val providerUnit = ProviderUnitConverter.convert(request).copy(id = id)
            val providerUnitAddress = StructuredAddress(
                street = request.street,
                number = request.number,
                complement = request.complement,
                neighborhood = request.neighborhood,
                city = request.city,
                state = request.state,
                zipcode = request.zipcode,
                latitude = request.latitude,
                longitude = request.longitude

            )
            val expectedResponseFromWrite = ProviderUnitResponseConverter.convert(
                ProviderUnitWithAdditionalInfoResponse(
                    providerUnit = providerUnit,
                    address = providerUnitAddress,
                    livance = livanceIntegration
                )
            )

            coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
            coEvery {
                partnerIntegrationService.getByProviderUnitIdAndProviderType(
                    providerUnit.id,
                    PartnerIntegrationProviderType.LIVANCE
                )
            } returns NotFoundException("not_found").failure()
            coEvery {
                partnerIntegrationService.create(partnerIntegration)
            } returns partnerIntegration.success()
            coEvery {
                providerUnitService.updateWithAddress(providerUnit, providerUnitAddress)
            } returns providerUnit.copy(
                address = providerUnitAddress.copy(
                    referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
                    referencedModelId = providerUnit.id,
                )
            ).success()

            authenticatedAs(idToken, staff) {
                put(to = "/v2/provider_units/${providerUnit.id}", body = request) { response ->
                    assertThat(response).isOKWithData(expectedResponseFromWrite)

                    coVerifyOnce { providerUnitService.get(any()) }
                    coVerifyOnce { providerUnitService.updateWithAddress(any(), any()) }
                    coVerifyOnce { partnerIntegrationService.getByProviderUnitIdAndProviderType(any(), any()) }
                    coVerifyOnce { partnerIntegrationService.create(any()) }

                    confirmVerified(
                        providerUnitService,
                        providerUnitGroupService,
                        partnerIntegrationService
                    )
                }
            }
        }

    @Test
    fun `#update should update ProviderUnit and update current partner integration provider unit`() =
        mockRangeUuidAndDateTime { id, _ ->
            val livanceLocationId = "123"
            val partnerIntegration = PartnerIntegrationProviderUnit(
                providerType = PartnerIntegrationProviderType.LIVANCE,
                providerUnitId = id,
                externalId = "321"
            )

            val request = request.copy(
                medicalSpecialtyProfile = medicalSpecialtyProfileList,
                attendanceTypes = listOf(
                    ProviderUnit.AttendanceType.EMERGENCY_CARE
                ),
                livance = LivanceIntegrationTransport(livanceLocationId)
            )
            val providerUnit = ProviderUnitConverter.convert(request).copy(id = id)
            val providerUnitAddress = StructuredAddress(
                street = request.street,
                number = request.number,
                complement = request.complement,
                neighborhood = request.neighborhood,
                city = request.city,
                state = request.state,
                zipcode = request.zipcode,
                latitude = request.latitude,
                longitude = request.longitude

            )
            val expectedResponseFromWrite = ProviderUnitResponseConverter.convert(
                ProviderUnitWithAdditionalInfoResponse(
                    providerUnit = providerUnit,
                    address = providerUnitAddress,
                    livance = LivanceIntegrationTransport(livanceLocationId)
                )
            )

            val updatedPartnerIntegration = partnerIntegration.copy(externalId = livanceLocationId)

            coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
            coEvery {
                partnerIntegrationService.getByProviderUnitIdAndProviderType(
                    providerUnit.id,
                    PartnerIntegrationProviderType.LIVANCE
                )
            } returns partnerIntegration.success()
            coEvery {
                partnerIntegrationService.update(updatedPartnerIntegration)
            } returns updatedPartnerIntegration.success()
            coEvery {
                providerUnitService.updateWithAddress(providerUnit, providerUnitAddress)
            } returns providerUnit.copy(
                address = providerUnitAddress.copy(
                    referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
                    referencedModelId = providerUnit.id,
                )
            ).success()

            authenticatedAs(idToken, staff) {
                put(to = "/v2/provider_units/${providerUnit.id}", body = request) { response ->
                    assertThat(response).isOKWithData(expectedResponseFromWrite)

                    coVerifyOnce { providerUnitService.get(any()) }
                    coVerifyOnce { providerUnitService.updateWithAddress(any(), any()) }
                    coVerifyOnce { partnerIntegrationService.getByProviderUnitIdAndProviderType(any(), any()) }
                    coVerifyOnce { partnerIntegrationService.update(any()) }

                    confirmVerified(
                        providerUnitService,
                        providerUnitGroupService,
                        partnerIntegrationService
                    )
                }
            }
        }

    @Test
    fun `#update should update ProviderUnit and delete a partner integration provider unit`() =
        mockRangeUuidAndDateTime { id, _ ->
            val livanceLocationId = "321"
            val partnerIntegration = PartnerIntegrationProviderUnit(
                providerType = PartnerIntegrationProviderType.LIVANCE,
                providerUnitId = id,
                externalId = livanceLocationId
            )

            val request = request.copy(
                medicalSpecialtyProfile = medicalSpecialtyProfileList,
                attendanceTypes = listOf(
                    ProviderUnit.AttendanceType.EMERGENCY_CARE
                )
            )
            val providerUnit = ProviderUnitConverter.convert(request).copy(id = id)
            val providerUnitAddress = StructuredAddress(
                street = request.street,
                number = request.number,
                complement = request.complement,
                neighborhood = request.neighborhood,
                city = request.city,
                state = request.state,
                zipcode = request.zipcode,
                latitude = request.latitude,
                longitude = request.longitude

            )
            val expectedResponseFromWrite = ProviderUnitResponseConverter.convert(
                ProviderUnitWithAdditionalInfoResponse(
                    providerUnit = providerUnit,
                    address = providerUnitAddress
                )
            )

            coEvery { providerUnitService.get(providerUnit.id) } returns providerUnit.success()
            coEvery {
                partnerIntegrationService.getByProviderUnitIdAndProviderType(
                    providerUnit.id,
                    PartnerIntegrationProviderType.LIVANCE
                )
            } returns partnerIntegration.success()
            coEvery {
                partnerIntegrationService.delete(partnerIntegration)
            } returns true.success()
            coEvery {
                providerUnitService.updateWithAddress(providerUnit, providerUnitAddress)
            } returns providerUnit.copy(
                address = providerUnitAddress.copy(
                    referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
                    referencedModelId = providerUnit.id,
                )
            ).success()

            authenticatedAs(idToken, staff) {
                put(to = "/v2/provider_units/${providerUnit.id}", body = request) { response ->
                    assertThat(response).isOKWithData(expectedResponseFromWrite)

                    coVerifyOnce { providerUnitService.get(any()) }
                    coVerifyOnce { providerUnitService.updateWithAddress(any(), any()) }
                    coVerifyOnce { partnerIntegrationService.getByProviderUnitIdAndProviderType(any(), any()) }
                    coVerifyOnce { partnerIntegrationService.delete(any()) }

                    confirmVerified(
                        providerUnitService,
                        providerUnitGroupService,
                        partnerIntegrationService
                    )
                }
            }
        }

    @Test
    fun `#update should update ProviderUnit to inactive`() {
        val request = request.copy(
            medicalSpecialtyProfile = medicalSpecialtyProfileList,
            attendanceTypes = listOf(
                ProviderUnit.AttendanceType.EMERGENCY_CARE
            ),
            status = Status.INACTIVE
        )
        val providerUnitFromRequest = ProviderUnitConverter.convert(request)
        assertThat(providerUnitFromRequest.status).isEqualTo(request.status)
        val providerUnitAddress = StructuredAddress(
            street = request.street,
            number = request.number,
            complement = request.complement,
            neighborhood = request.neighborhood,
            city = request.city,
            state = request.state,
            zipcode = request.zipcode,
            latitude = request.latitude,
            longitude = request.longitude

        )
        val expectedResponseFromWrite = ProviderUnitResponseConverter.convert(
            ProviderUnitWithAdditionalInfoResponse(
                providerUnit = providerUnitFromRequest,
                address = providerUnitAddress,
            )
        )

        coEvery { providerUnitService.get(providerUnitFromRequest.id) } returns providerUnitFromRequest.success()
        coEvery {
            partnerIntegrationService.getByProviderUnitIdAndProviderType(
                providerUnitFromRequest.id,
                PartnerIntegrationProviderType.LIVANCE
            )
        } returns NotFoundException("not_found").failure()
        coEvery {
            providerUnitService.updateWithAddress(
                match {
                    it == providerUnitFromRequest.copy(id = it.id, createdAt = it.createdAt, updatedAt = it.updatedAt)
                },
                match {
                    it == providerUnitAddress.copy(id = it.id, createdAt = it.createdAt, updatedAt = it.updatedAt)
                }
            )
        } returns providerUnitFromRequest.copy(
            address = providerUnitAddress.copy(
                referencedModelClass = StructuredAddressReferenceModel.PROVIDER_UNIT,
                referencedModelId = providerUnitFromRequest.id,
            )
        ).success()

        authenticatedAs(idToken, staff) {
            put(to = "/v2/provider_units/${providerUnitFromRequest.id}", body = request) { response ->
                assertThat(response).isOKWithData(expectedResponseFromWrite)

                coVerifyOnce { providerUnitService.get(any()) }
                coVerifyOnce { providerUnitService.updateWithAddress(any(), any()) }
                coVerifyOnce { partnerIntegrationService.getByProviderUnitIdAndProviderType(any(), any()) }
            }
        }

    }

    @Test
    fun `#Should return ProviderUnitGroups empty lists`() {
        coEvery { providerUnitGroupService.getAll() } returns
                emptyList<ProviderUnitGroup>().success()

        authenticatedAs(idToken, staff) {
            get("/provider_unit_groups_title") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<InputList> = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedEmptyInputList)
            }
        }
    }

    @Test
    fun `#Should return ProviderUnitGroups`() {
        coEvery { providerUnitGroupService.getAll() } returns
                listOf(providerUnitGroup).success()

        authenticatedAs(idToken, staff) {
            get("/provider_unit_groups_title") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<InputList> = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedProviderUnitGroupInputList)
            }
        }
    }

    @Test
    fun `#Should return bank institutions`() {
        val expectedResponse = BankInstitutions.values().map {
            InputList(name = it.description, id = it.code)
        }
        authenticatedAs(idToken, staff) {
            get("/bank_institutions") { response ->
                assertThat(response).isOKWithData(expectedResponse)
            }
        }
    }

}
