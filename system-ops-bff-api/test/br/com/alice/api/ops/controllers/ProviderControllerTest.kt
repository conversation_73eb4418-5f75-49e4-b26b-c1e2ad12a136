package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.converters.ProviderResponseConverter
import br.com.alice.api.ops.models.ProviderRequest
import br.com.alice.api.ops.models.ProviderResponse
import br.com.alice.common.core.Status
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ProviderType
import br.com.alice.provider.client.ProviderFilter
import br.com.alice.provider.client.ProviderService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class ProviderControllerTest : ControllerTestHelper() {

    private val providerService: ProviderService = mockk()
    private val controller = ProviderController(
        providerService
    )

    private val provider = TestModelFactory.buildProvider()
    private val expectedResponse = ProviderResponseConverter.convert(
        provider
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearAllMocks()
        module.single { controller }
    }

    val range = IntRange(0, 49)

    @Test
    fun `#index should list Providers by name filter using provider domain`() {
        val filter = ProviderFilter(searchToken = "oswaldo")
        coEvery { providerService.getByFiltersWithRange(filter, range) } returns listOf(provider).success()
        coEvery { providerService.countByFilters(filter) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/providers?filter={\"q\":\"oswaldo\"}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<ProviderResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
            }
        }
    }

    @Test
    fun `#index should list Providers by range`() {
        val filter = ProviderFilter()
        coEvery { providerService.getByFiltersWithRange(filter, range) } returns
                listOf(provider).success()

        coEvery { providerService.countByFilters(filter) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/providers?filter={}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<ProviderResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(provider.id)
            }
        }
    }

    @Test
    fun `#index should list Providers by status list filter using provider domain`() {
        val filter = ProviderFilter(status = listOf(Status.ACTIVE, Status.INACTIVE))
        coEvery { providerService.getByFiltersWithRange(filter, range) } returns listOf(provider).success()
        coEvery { providerService.countByFilters(filter) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/providers?filter={\"status\":\"ACTIVE,INACTIVE\"}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<ProviderResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
            }
        }
    }
    @Test
    fun `#index should list Providers by status filter using provider domain`() {
        val filter = ProviderFilter(status = listOf(Status.ACTIVE))
        coEvery { providerService.getByFiltersWithRange(filter, range) } returns listOf(provider).success()
        coEvery { providerService.countByFilters(filter) } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/providers?filter={\"status\":\"ACTIVE\"}&range=[0,49]&sort=[\"id\",\"DESC\"]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<ProviderResponse> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(1)
            }
        }
    }

    @Test
    fun `#getById should get Provider`() {
        coEvery { providerService.get(provider.id) } returns provider.success()

        authenticatedAs(idToken, staff) {
            get("/providers/${provider.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: ProviderResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }
    }

    @Test
    fun `#create should create Provider`() {
        val request = ProviderRequest(
            name = "Oswaldo Cruz",
            site = null,
            cnpj = null,
            imageUrl = null,
            phones = emptyList(),
            type = ProviderType.HOSPITAL,
            flagship = false,
            urlSlug = "some_url_slug",
            daysForPayment = 30,
            description = "description",
            icon = "icon",
            logo = "logo",
            thumbnail = "thumbnail",
            about = "about",
            status = null
        )
        authenticatedAs(idToken, staff) {
            coEvery { providerService.add(any()) } returns provider.success()

            post(to = "/providers", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: ProviderResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse)
            }
        }

    }

    @Test
    fun `#update should update Provider`() {
        val request = ProviderRequest(
            name = "Oswaldo Cruz",
            site = null,
            cnpj = null,
            imageUrl = null,
            phones = emptyList(),
            type = ProviderType.HOSPITAL,
            flagship = true,
            urlSlug = null,
            daysForPayment = 30,
            description = "description",
            icon = "icon",
            logo = "logo",
            thumbnail = "thumbnail",
            about = "about",
        )

        coEvery { providerService.get(provider.id) } returns provider.success()

        coEvery {
            providerService.update(match {
                it.id == provider.id
                        && it.name == request.name
                        && it.flagship == request.flagship
            })
        } returns provider.copy(name = request.name, flagship = true).success()

        authenticatedAs(idToken, staff) {
            put(to = "/providers/${provider.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: ProviderResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expectedResponse.copy(name = request.name, flagship = true))
            }
        }
    }

}
