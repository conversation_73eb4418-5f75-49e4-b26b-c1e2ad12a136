package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.services.PolicyDescriptionDataService
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class PolicyDescriptionControllerTest : ControllerTestHelper() {

    private val policyDescriptionDataService: PolicyDescriptionDataService = mockk()
    private val policyDescriptionController = PolicyDescriptionController(policyDescriptionDataService)

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(policyDescriptionDataService)
        module.single { policyDescriptionController }
    }

    @Test
    fun `#index should get by all Policies Descriptions sorted`() {
        coEvery {
            policyDescriptionDataService.getAllPoliciesDescriptions()
        } returns listOf(
            "at ehr-api Physician can view any Appointment",
            "at ehr-api Physician can view any ClinicalBackground",
            "at ehr-api Physician can update any Appointment",
            "at ehr-api Physician can update any ClinicalBackground",
            "at ehr-api Physician can count Appointments",
            "at ehr-api Person can view her own Appointment",
            "at ehr-api Person can view ClinicalBackground of herself",
            "at ehr-api Unauthenticated can view, count any Company",
            "at ehr-api Unauthenticated can view any Device"
        ).success()

        authenticatedAs(idToken, staff) {
            get("/policyDescription") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<PolicyDescriptionResponse> = response.bodyAsJson()
                assertThat(content).hasSize(9)

                assertThat(content.first().description).isEqualTo("at ehr-api Person can view ClinicalBackground of herself")
                assertThat(content.last().description).isEqualTo("at ehr-api Unauthenticated can view, count any Company")

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("9")
            }
        }
    }

}
