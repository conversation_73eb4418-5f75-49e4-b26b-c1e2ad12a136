package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthPlanTaskGroupTemplate
import br.com.alice.healthplan.client.HealthPlanTaskGroupTemplateService
import br.com.alice.healthplan.client.HealthPlanTaskTemplateService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions
import kotlin.test.BeforeTest
import kotlin.test.Test

internal class HealthPlanTaskGroupTemplateControllerTest : ControllerTestHelper() {
    private val groupTemplateService: HealthPlanTaskGroupTemplateService = mockk()
    private val healthPlanTaskTemplateService: HealthPlanTaskTemplateService = mockk()

    val controller = HealthPlanTaskGroupTemplateController(groupTemplateService, healthPlanTaskTemplateService)
    private val groupTemplate = HealthPlanTaskGroupTemplate(
        name = "Exames de rotina",
        taskTemplateIds = listOf(RangeUUID.generate()),
        active = true
    )
    private val taskTemplate = TestModelFactory.buildHealthPlanTaskTemplate()

    private val request = HealthPlanTaskGroupTemplateRequest(
        name = "Exames de rotina",
        templateIds = groupTemplate.taskTemplateIds,
        active = true
    )

    private val enrichedResponse = HealthPlanTaskGroupTemplateResponse(
        id = groupTemplate.id,
        name = groupTemplate.name,
        templates = listOf(HealthPlanTaskTemplateResponse(
            id = taskTemplate.id,
            name = taskTemplate.title
        )),
        active = groupTemplate.active
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { controller }
    }

    @Test
    fun `#index returns found templates`() = runBlocking {
        coEvery { groupTemplateService.findFiltered(IntRange(0, 9), "Rotina") } returns listOf(groupTemplate).success()
        coEvery { groupTemplateService.countFiltered("Rotina") } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/health_plan_task_group_templates?range=[0,9]&filter={name:Rotina}") { response ->
                assertThat(response).isOKWithData(listOf(groupTemplate))
                Assertions.assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }
    }

    @Test
    fun `#index returns failure if cannot find templates`() = runBlocking {
        coEvery { groupTemplateService.findFiltered(IntRange(0, 9), "Rotina") } returns NotFoundException().failure()

        authenticatedAs(idToken, staff) {
            get("/health_plan_task_group_templates?range=[0,9]&filter={name:Rotina}") { response ->
                assertThat(response).isNotFound()
            }
        }
        coVerify(exactly = 0) { groupTemplateService.countFiltered(any()) }
    }

    @Test
    fun `#index returns failure if cannot count templates`() = runBlocking {
        coEvery { groupTemplateService.findFiltered(IntRange(0, 9), "Rotina") } returns listOf(groupTemplate).success()
        coEvery { groupTemplateService.countFiltered("Rotina") } returns Exception("").failure()

        authenticatedAs(idToken, staff) {
            get("/health_plan_task_group_templates?range=[0,9]&filter={name:Rotina}") { response ->
                assertThat(response).isInternalServerError()
            }
        }
    }

    @Test
    fun `#get returns found template`() = runBlocking {
        coEvery { groupTemplateService.get(groupTemplate.id) } returns groupTemplate.success()
        coEvery {
            healthPlanTaskTemplateService.findByIds(groupTemplate.taskTemplateIds)
        } returns listOf(taskTemplate).success()

        authenticatedAs(idToken, staff) {
            get("/health_plan_task_group_templates/${groupTemplate.id}") { response ->
                assertThat(response).isOKWithData(enrichedResponse)
            }
        }
    }

    @Test
    fun `#create returns created template`() = runBlocking {
        coEvery { groupTemplateService.create(
            name = request.name,
            templateIds = request.templateIds,
            active = request.active
        ) } returns groupTemplate.success()

        authenticatedAs(idToken, staff) {
            post("/health_plan_task_group_templates", request) { response ->
                assertThat(response).isOKWithData(groupTemplate)
            }
        }
    }

    @Test
    fun `#update returns updated template`() = runBlocking {
        coEvery { groupTemplateService.update(
            id = groupTemplate.id,
            name = request.name,
            templateIds = request.templateIds,
            active = request.active
        ) } returns groupTemplate.success()
        coEvery {
            healthPlanTaskTemplateService.findByIds(groupTemplate.taskTemplateIds)
        } returns listOf(taskTemplate).success()

        authenticatedAs(idToken, staff) {
            put("/health_plan_task_group_templates/${groupTemplate.id}", request) { response ->
                assertThat(response).isOKWithData(enrichedResponse)
            }
        }
    }

    @Test
    fun `#search returns found templates`() = runBlocking {
        coEvery { groupTemplateService.search(
            query = "Rotina",
            sort = Pair("active", "DESC")
        ) } returns listOf(groupTemplate).success()

        authenticatedAs(idToken, staff) {
            get("/health_plan_task_group_templates/search?filter={q:Rotina}&sort=[\"active\",\"DESC\"]") { response ->
                assertThat(response).isOKWithData(listOf(groupTemplate))
            }
        }
    }
}
