package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.TestCode
import br.com.alice.provider.client.TestCodeService
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class TestCodeControllerTest : ControllerTestHelper() {

    private val testCodeService: TestCodeService = mockk()
    private val testCodeController = TestCodeController(testCodeService)

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(testCodeService)
        module.single { testCodeController }
    }

    private val testCode = TestModelFactory.buildTestCode()
    private val testCodeUpdated = testCode.copy(description = "newDescription", preparationId = RangeUUID.generate())
    private val request = UpsertTestCodeRequest(
        code = testCodeUpdated.code,
        description = testCodeUpdated.description,
        internalDescription = testCodeUpdated.internalDescription,
        priority = testCodeUpdated.priority,
        synonyms = listOf("abc"),
        preparationId = testCodeUpdated.preparationId.toString(),
        resultExpirationInDays = 180,
        sensitiveResult = testCodeUpdated.sensitiveResult,
        active = testCodeUpdated.active,
    )

    @Test
    fun `#create should create TestCode`() {
        authenticatedAs(idToken, staff) {
            coEvery {
                testCodeService.upsert(match {
                    it.code == testCodeUpdated.code
                            && it.description == testCodeUpdated.description
                            && it.internalDescription == testCodeUpdated.internalDescription
                            && it.priority == testCodeUpdated.priority
                            && it.preparationId == testCodeUpdated.preparationId
                            && it.active == testCodeUpdated.active
                })
            } returns testCodeUpdated.success()

            post(to = "/test_codes", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: TestCode = response.bodyAsJson()
                assertThat(content.code).isEqualTo(testCodeUpdated.code)
                assertThat(content.description).isEqualTo(testCodeUpdated.description)
                assertThat(content.internalDescription).isEqualTo(testCodeUpdated.internalDescription)
                assertThat(content.priority).isEqualTo(testCodeUpdated.priority)
                assertThat(content.synonyms).isEqualTo(testCodeUpdated.synonyms)
                assertThat(content.preparationId).isEqualTo(testCodeUpdated.preparationId)
                assertThat(content.sensitiveResult).isEqualTo(testCode.sensitiveResult)
                assertThat(content.resultExpirationInDays).isEqualTo(testCode.resultExpirationInDays)
                assertThat(content.active).isEqualTo(testCode.active)
            }
        }
    }

    @Test
    fun `#index should get TestCodes by range`() {
        val testCode2 = testCode.copy(id = RangeUUID.generate())

        coEvery { testCodeService.findAllByRange(IntRange(0, 49), any()) } returns listOf(testCode, testCode2).success()

        coEvery { testCodeService.count() } returns 10.success()

        authenticatedAs(idToken, staff) {
            get("/test_codes?filter={}&range=[0,49]") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<TestCode> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)

                assertThat(content[0].id).isEqualTo(testCode.id)
                assertThat(content[0].code).isEqualTo(testCode.code)
                assertThat(content[0].preparationId).isEqualTo(testCode.preparationId)
                assertThat(content[0].resultExpirationInDays).isEqualTo(testCode.resultExpirationInDays)

                assertThat(content[1].id).isEqualTo(testCode2.id)
                assertThat(content[1].code).isEqualTo(testCode2.code)
                assertThat(content[1].preparationId).isEqualTo(testCode2.preparationId)
                assertThat(content[1].resultExpirationInDays).isEqualTo(testCode2.resultExpirationInDays)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("10")

                coVerify(exactly = 1) {
                    testCodeService.findAllByRange(IntRange(0, 49), null)
                }
            }
        }
    }

    @Test
    fun `#index should get TestCodes by query`() {
        val testCode2 = testCode.copy(id = RangeUUID.generate())

        coEvery { testCodeService.findBySearchTokens("somecode", any()) } returns listOf(testCode, testCode2).success()

        authenticatedAs(idToken, staff) {
            get("/test_codes?filter={\"q\":\"somecode\"}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<TestCode> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)

                assertThat(content[0].id).isEqualTo(testCode.id)
                assertThat(content[0].code).isEqualTo(testCode.code)
                assertThat(content[0].preparationId).isEqualTo(testCode.preparationId)

                assertThat(content[1].id).isEqualTo(testCode2.id)
                assertThat(content[1].code).isEqualTo(testCode2.code)
                assertThat(content[1].preparationId).isEqualTo(testCode2.preparationId)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")

                coVerify(exactly = 1) {
                    testCodeService.findBySearchTokens("somecode", null)
                }
            }
        }
    }

    @Test
    fun `#index should get TestCodes by query and inactive`() {
        val testCode2 = testCode.copy(id = RangeUUID.generate())

        coEvery { testCodeService.findBySearchTokens("somecode", false) } returns listOf(testCode, testCode2).success()

        authenticatedAs(idToken, staff) {
            get("/test_codes?filter={\"q\":\"somecode\", \"active\": \"false\"}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<TestCode> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)

                assertThat(content[0].id).isEqualTo(testCode.id)
                assertThat(content[0].code).isEqualTo(testCode.code)
                assertThat(content[0].preparationId).isEqualTo(testCode.preparationId)

                assertThat(content[1].id).isEqualTo(testCode2.id)
                assertThat(content[1].code).isEqualTo(testCode2.code)
                assertThat(content[1].preparationId).isEqualTo(testCode2.preparationId)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }
    }

    @Test
    fun `#index should get TestCodes by ids`() {
        val testCode2 = testCode.copy(id = RangeUUID.generate())

        coEvery { testCodeService.findByIds(listOf(testCode.id, testCode2.id)) } returns listOf(
            testCode,
            testCode2
        ).success()

        coEvery { testCodeService.count() } returns 2.success()

        authenticatedAs(idToken, staff) {
            get("/test_codes?filter={\"test_code_ids\":[\"${testCode.id}\",\"${testCode2.id}\"]}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: List<TestCode> = response.bodyAsJson()
                assertThat(content.size).isEqualTo(2)
                val responseIds = content.map { it.id }
                assertThat(responseIds).contains(testCode.id)
                assertThat(responseIds).contains(testCode2.id)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }
    }

    @Test
    fun `#getById should get TestCode by id`() {
        coEvery { testCodeService.get(testCode.id) } returns testCode.success()

        authenticatedAs(idToken, staff) {
            get("/test_codes/${testCode.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: TestCode = response.bodyAsJson()
                assertThat(content.id).isEqualTo(testCode.id)
                assertThat(content.code).isEqualTo(testCode.code)
                assertThat(content.description).isEqualTo(testCode.description)
                assertThat(content.synonyms).isEqualTo(testCode.synonyms)
                assertThat(content.preparationId).isEqualTo(testCode.preparationId)
                assertThat(content.sensitiveResult).isEqualTo(testCode.sensitiveResult)
                assertThat(content.resultExpirationInDays).isEqualTo(testCode.resultExpirationInDays)
                assertThat(content.active).isEqualTo(testCode.active)
            }
        }
    }

    @Test
    fun `#update should update TestCode`() {
        val newCode = "12345678"
        val request = request.copy(code = newCode, allowChangeCode = null)
        coEvery { testCodeService.get(testCode.id) } returns testCode.success()
        coEvery {
            testCodeService.upsert(match {
                it.code == testCodeUpdated.code
                        && it.description == testCodeUpdated.description
                        && it.preparationId == testCodeUpdated.preparationId
            })
        } returns testCodeUpdated.success()

        authenticatedAs(idToken, staff) {
            put(to = "/test_codes/${testCode.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: TestCode = response.bodyAsJson()
                assertThat(content.id).isEqualTo(testCodeUpdated.id)
                assertThat(content.code).isEqualTo(testCodeUpdated.code)
                assertThat(content.description).isEqualTo(testCodeUpdated.description)
                assertThat(content.synonyms).isEqualTo(testCodeUpdated.synonyms)
                assertThat(content.preparationId).isEqualTo(testCodeUpdated.preparationId)
                assertThat(content.sensitiveResult).isEqualTo(testCode.sensitiveResult)
                assertThat(content.resultExpirationInDays).isEqualTo(testCode.resultExpirationInDays)
            }
        }
    }

    @Test
    fun `#update should update TestCode allow code true`() {
        val newCode = "12345678"
        coEvery { testCodeService.get(testCode.id) } returns testCode.success()
        coEvery {
            testCodeService.upsert(match {
                it.code == newCode
                        && it.description == testCodeUpdated.description
                        && it.preparationId == testCodeUpdated.preparationId
            })
        } returns testCodeUpdated.success()
        val request = request.copy(code = newCode, allowChangeCode = true)

        authenticatedAs(idToken, staff) {
            put(to = "/test_codes/${testCode.id}?allow_change_code=true", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: TestCode = response.bodyAsJson()
                assertThat(content.id).isEqualTo(testCodeUpdated.id)
                assertThat(content.code).isEqualTo(testCodeUpdated.code)
                assertThat(content.description).isEqualTo(testCodeUpdated.description)
                assertThat(content.synonyms).isEqualTo(testCodeUpdated.synonyms)
                assertThat(content.preparationId).isEqualTo(testCodeUpdated.preparationId)
                assertThat(content.sensitiveResult).isEqualTo(testCode.sensitiveResult)
                assertThat(content.resultExpirationInDays).isEqualTo(testCode.resultExpirationInDays)
            }
        }
    }

}
