package br.com.alice.api.ops.controllers.healthcondition

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.models.HealthConditionRequest
import br.com.alice.api.ops.models.HealthConditionResponse
import br.com.alice.api.ops.models.HealthConditionTemplateRequest
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthCondition
import br.com.alice.data.layer.models.HealthConditionCodeType
import br.com.alice.healthcondition.client.HealthConditionService
import br.com.alice.healthcondition.client.HealthConditionTemplateService
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.CapturingSlot
import io.mockk.coEvery
import io.mockk.confirmVerified
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthConditionControllerTest : ControllerTestHelper() {

    private val healthConditionService: HealthConditionService = mockk()
    private val healthConditionTemplateService: HealthConditionTemplateService = mockk()
    private val healthConditionController = HealthConditionController(healthConditionService, healthConditionTemplateService)

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { healthConditionController }
    }

    private val healthCondition = TestModelFactory.buildHealthCondition()
    private val healthConditionUpdated = healthCondition.copy(name = "Desvio de septo", code = "J342")

    private val request = healthConditionUpdated.convertTo(HealthConditionRequest::class)
    private val responseModel = healthConditionUpdated.convertTo(HealthConditionResponse::class)

    @AfterTest
    fun confirmMocks() = confirmVerified(healthConditionService)

    @Test
    fun `#create should create HealthCondition`() {
        authenticatedAs(idToken, staff) {
            val healthConditionSlot = CapturingSlot<HealthCondition>()
            coEvery { healthConditionService.add(capture(healthConditionSlot)) } returns healthConditionUpdated.success()

            post(to = "/health_conditions", body = request) { response ->
                assertThat(response).isOKWithData(responseModel)
                assertThat(healthConditionSlot.captured).usingRecursiveComparison().ignoringFields("id", "createdAt", "updatedAt").isEqualTo(healthConditionUpdated)
            }
        }

        coVerifyOnce { healthConditionService.add(any()) }
    }

    @Test
    fun `#create should add template when it is in the request`() {
        val healthConditionTemplate = TestModelFactory.buildHealthConditionTemplate()
        val healthConditionTemplateRequest = HealthConditionTemplateRequest(healthConditionId = healthConditionTemplate.healthConditionId, template = healthConditionTemplate.template)
        val requestWithTemplate = request.copy(
            healthConditionTemplate = healthConditionTemplateRequest
        )
        val expectedResponse = responseModel.copy(healthConditionTemplate = healthConditionTemplate)
        val healthConditionUpdatedWithTemplate = healthConditionUpdated.copy(healthConditionTemplate = healthConditionTemplate)

        authenticatedAs(idToken, staff) {
            val healthConditionSlot = CapturingSlot<HealthCondition>()
            coEvery { healthConditionService.add(capture(healthConditionSlot)) } returns healthConditionUpdatedWithTemplate.success()
            coEvery { healthConditionTemplateService.upsert(any()) } returns healthConditionUpdatedWithTemplate.healthConditionTemplate!!.success()

            post(to = "/health_conditions", body = requestWithTemplate) { response ->
                assertThat(response).isOKWithData(expectedResponse)
                assertThat(healthConditionSlot.captured)
                    .usingRecursiveComparison()
                    .ignoringFields("id", "createdAt", "updatedAt", "healthConditionTemplate.id", "healthConditionTemplate.createdAt", "healthConditionTemplate.updatedAt")
                    .isEqualTo(healthConditionUpdatedWithTemplate)
            }
        }

        coVerifyOnce { healthConditionService.add(any()) }
        coVerifyOnce { healthConditionTemplateService.upsert(any()) }
    }

    @Test
    fun `#index should get HealthCondition by range`() {
        val healthConditions = listOf(
            healthCondition,
            healthCondition.copy(id = RangeUUID.generate())
        )
        val expected = healthConditions.map { it.convertTo(HealthConditionResponse::class) }

        coEvery { healthConditionService.getByRange(0..49) } returns healthConditions.success()
        coEvery { healthConditionService.count() } returns 10.success()

        authenticatedAs(idToken, staff) {
            get("/health_conditions?filter={}&range=[0,49]") { response ->
                assertThat(response).isOKWithData(expected)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("10")
            }
        }

        coVerifyOnce { healthConditionService.getByRange(any()) }
        coVerifyOnce { healthConditionService.count() }
    }

    @Test
    fun `#index should get HealthCondition by query`() {
        val healthConditions = listOf(
            healthCondition,
            healthCondition.copy(id = RangeUUID.generate())
        )
        val expected = healthConditions.map { it.convertTo(HealthConditionResponse::class) }

        coEvery {
            healthConditionService.search("somecode", includeInactive = true)
        } returns healthConditions.success()

        authenticatedAs(idToken, staff) {
            get("/health_conditions?filter={\"q\":\"somecode\"}") { response ->
                assertThat(response).isOKWithData(expected)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }

        coVerifyOnce { healthConditionService.search(any(), any()) }
    }

    @Test
    fun `#index should get HealthCondition by query and type`() {
        val healthConditions = listOf(
            healthCondition,
            healthCondition.copy(id = RangeUUID.generate())
        )
        val expected = healthConditions.map { it.convertTo(HealthConditionResponse::class) }

        coEvery {
            healthConditionService.searchByType(
                query = "somecode",
                types = listOf(HealthConditionCodeType.CID_10, HealthConditionCodeType.CIAP_2)
            )
        } returns healthConditions.success()

        authenticatedAs(idToken, staff) {
            get("/health_conditions?filter={\"q\":\"somecode\", \"types\":[\"CID_10\",\"CIAP_2\"]}") { response ->
                assertThat(response).isOKWithData(expected)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }

        coVerifyOnce { healthConditionService.searchByType(any(), any()) }
    }

    @Test
    fun `#index should get HealthCondition by range when filter have only types`() {
        val healthConditions = listOf(
            healthCondition,
            healthCondition.copy(id = RangeUUID.generate())
        )
        val expected = healthConditions.map { it.convertTo(HealthConditionResponse::class) }

        coEvery { healthConditionService.getByRange(0..19) } returns healthConditions.success()
        coEvery { healthConditionService.count() } returns 10.success()


        authenticatedAs(idToken, staff) {
            get("/health_conditions?filter={\"types\":[\"CID_10\",\"CIAP_2\"]}") { response ->
                assertThat(response).isOKWithData(expected)

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("10")
            }
        }

        coVerifyOnce { healthConditionService.getByRange(any()) }
        coVerifyOnce { healthConditionService.count() }
    }

    @Test
    fun `#getById should get HealthCondition by id`() {
        coEvery { healthConditionService.findById(healthCondition.id, HealthConditionService.FindOptions(true)) } returns healthCondition.success()

        authenticatedAs(idToken, staff) {
            get("/health_conditions/${healthCondition.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: HealthConditionResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(healthCondition.convertTo(HealthConditionResponse::class))
            }
        }

        coVerifyOnce { healthConditionService.findById(any(), any()) }
    }

    @Test
    fun `#update should update HealthCondition`() {
        coEvery { healthConditionService.findById(healthCondition.id, HealthConditionService.FindOptions(true)) } returns healthCondition.success()
        coEvery {
            healthConditionService.update(match {
                it.id == healthCondition.id
            })
        } returns healthConditionUpdated.success()

        authenticatedAs(idToken, staff) {
            put(to = "/health_conditions/${healthCondition.id}", body = request) { response ->
                assertThat(response).isSuccessfulJson()

                val content: HealthConditionResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(healthConditionUpdated.convertTo(HealthConditionResponse::class))
            }
        }

        coVerifyOnce { healthConditionService.findById(any(), any()) }
        coVerifyOnce { healthConditionService.update(any()) }
    }

    @Test
    fun `#delete should remove HealthCondition`() {
        coEvery {
            healthConditionService.delete(healthCondition.id)
        } returns true.success()

        authenticatedAs(idToken, staff) {
            delete("/health_conditions/${healthCondition.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val content: Boolean = response.bodyAsJson()
                assertThat(content).isTrue()
            }
        }

        coVerifyOnce { healthConditionService.delete(any()) }
    }
}
