package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.models.OptionResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthForm
import br.com.alice.data.layer.models.HealthFormQuestion
import br.com.alice.data.layer.models.HealthFormSection
import br.com.alice.data.layer.models.ServiceScriptNodeType
import br.com.alice.data.layer.models.TestCodePackage
import br.com.alice.healthlogic.models.TriggerActionType
import br.com.alice.healthlogic.models.bud.NodeWithRelationships
import br.com.alice.provider.client.TestCodeService
import br.com.alice.questionnaire.client.HealthFormManagementService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders.ContentRange
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthFormAutomaticTaskEngineControllerTest : ControllerTestHelper() {
    private val healthFormManagementService: HealthFormManagementService = mockk()
    private val testCodeService: TestCodeService = mockk()

    private val healthFormAutomaticTaskController = HealthFormAutomaticTaskController(
        healthFormManagementService,
        testCodeService,
    )

    private val node = TestModelFactory.buildServiceScriptNode(
        name = TriggerActionType.HEALTH_FORM.name,
        type = ServiceScriptNodeType.TRIGGER
    )

    private val relationship = TestModelFactory.buildServiceScriptRelationship(
        parentId = node.id,
        childId = node.id
    )

    private val nodeWithRelationships = NodeWithRelationships(
        node,
        listOf(relationship)
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearAllMocks()
        module.single { healthFormAutomaticTaskController }
    }

    @Test
    fun `#getAllHealthForms - returns all health forms`() {
        val forms = listOf(TestModelFactory.buildHealthForm())

        coEvery { healthFormManagementService.getAllForms() } returns forms.success()

        authenticatedAs(idToken, staff) {
            get("/healthFormAutomaticTaskEngine/healthForms") { response ->
                assertThat(response).isSuccessfulJson()

                val content = response.bodyAsJson<List<HealthForm>>()
                assertThat(content).isEqualTo(forms)
                assertThat(response).containsHeaderWithValue(ContentRange, "1")
            }
        }
    }

    @Test
    fun `#getAllHealthForms - returns exception`() {
        coEvery { healthFormManagementService.getAllForms() } returns NotFoundException().failure()

        authenticatedAs(idToken, staff) {
            get("/healthFormAutomaticTaskEngine/healthForms") { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `#getSectionsByHealthFormId - returns health forms sections`() {
        val section = TestModelFactory.buildHealthFormSection()

        coEvery {
            healthFormManagementService.getSectionByFormId(section.healthFormId)
        } returns listOf(section).success()

        authenticatedAs(idToken, staff) {
            get(
                "/healthFormAutomaticTaskEngine/sections?filter={health_form_id=${section.healthFormId}}"
            ) { response ->
                assertThat(response).isSuccessfulJson()

                val content = response.bodyAsJson<List<HealthFormSection>>()
                assertThat(content).isEqualTo(listOf(section))
                assertThat(response).containsHeaderWithValue(ContentRange, "1")
            }
        }
    }

    @Test
    fun `#getSectionsByHealthFormId - returns empty if id on parameters`() {
        val id = RangeUUID.generate()
        authenticatedAs(idToken, staff) {
            get(
                "/healthFormAutomaticTaskEngine/sections?filter={id=[$id]}"
            ) { response ->
                assertThat(response).isSuccessfulJson()

                val content = response.bodyAsJson<List<HealthFormSection>>()
                assertThat(content).isEmpty()
                assertThat(response).containsHeaderWithValue(ContentRange, "0")
            }
        }
    }

    @Test
    fun `#getSectionsByHealthFormId - returns exception`() {
        val healthFormId = RangeUUID.generate()
        coEvery { healthFormManagementService.getSectionByFormId(healthFormId) } returns NotFoundException().failure()

        authenticatedAs(idToken, staff) {
            get(
                "/healthFormAutomaticTaskEngine/sections?filter={health_form_id=${healthFormId}}"
            ) { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `#getQuestionsBySectionId - returns all questions by section`() {
        val question = TestModelFactory.buildHealthFormQuestion()

        coEvery { healthFormManagementService.getQuestionBySectionId(question.healthFormSectionId) } returns listOf(question).success()

        authenticatedAs(idToken, staff) {
            get("/healthFormAutomaticTaskEngine/questions?filter={section_id=${question.healthFormSectionId}}") { response ->
                assertThat(response).isSuccessfulJson()

                val content = response.bodyAsJson<List<HealthFormQuestion>>()
                assertThat(content).isEqualTo(listOf(question))
                assertThat(response).containsHeaderWithValue(ContentRange, "1")
            }
        }
    }

    @Test
    fun `#getQuestionsBySectionId - returns empty if id on parameters`() {
        val id = RangeUUID.generate()
        authenticatedAs(idToken, staff) {
            get("/healthFormAutomaticTaskEngine/questions?filter={id=[$id]}") { response ->
                assertThat(response).isSuccessfulJson()

                val content = response.bodyAsJson<List<HealthFormQuestion>>()
                assertThat(content).isEmpty()
                assertThat(response).containsHeaderWithValue(ContentRange, "0")
            }
        }
    }

    @Test
    fun `#getQuestionsBySectionId - returns exception`() {
        val sectionId = RangeUUID.generate()
        coEvery { healthFormManagementService.getQuestionBySectionId(sectionId) } returns NotFoundException().failure()

        authenticatedAs(idToken, staff) {
            get("/healthFormAutomaticTaskEngine/questions?filter={section_id=${sectionId}}") { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `#getResponseOptions - returns health forms question options`() {
        val question = TestModelFactory.buildHealthFormQuestion()

        coEvery {
            healthFormManagementService.getQuestionById(question.id)
        } returns question.success()

        authenticatedAs(idToken, staff) {
            get(
                "/healthFormAutomaticTaskEngine/questions/options?filter={question_id=${question.id}}"
            ) { response ->
                assertThat(response).isSuccessfulJson()

                val content = response.bodyAsJson<List<OptionResponse>>()
                assertThat(content).isEqualTo(
                    question.options.map { OptionResponse(it.value.toString(), it.label.toString() )}
                )
                assertThat(response).containsHeaderWithValue(ContentRange, question.options.size.toString())
            }
        }
    }

    @Test
    fun `#getResponseOptions - returns empty if id on parameters`() {
        val id= RangeUUID.generate()

        authenticatedAs(idToken, staff) {
            get(
                "/healthFormAutomaticTaskEngine/questions/options?filter={id=[$id]}"
            ) { response ->
                assertThat(response).isSuccessfulJson()

                val content = response.bodyAsJson<List<OptionResponse>>()
                assertThat(content).isEmpty()
                assertThat(response).containsHeaderWithValue(ContentRange, "0")
            }
        }
    }

    @Test
    fun `#getResponseOptions - returns exception`() {
        val questionId = RangeUUID.generate()
        coEvery { healthFormManagementService.getQuestionById(questionId) } returns NotFoundException().failure()

        authenticatedAs(idToken, staff) {
            get("/healthFormAutomaticTaskEngine/questions/options?filter={question_id=${questionId}}") { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `#getAllTestPackages - returns all test packages`() {
        val packages = TestModelFactory.buildTestCodePackage()
        coEvery {
            testCodeService.findAllPackages()
        } returns listOf(packages).success()

        authenticatedAs(idToken, staff) {
            get("/healthFormAutomaticTaskEngine/testCodePackages") { response ->
                assertThat(response).isSuccessfulJson()

                val content = response.bodyAsJson<List<TestCodePackage>>()
                assertThat(content).isEqualTo(listOf(packages))
                assertThat(response).containsHeaderWithValue(ContentRange, "1")
            }
        }
    }

    @Test
    fun `#getAllTestPackages - returns exception`() {
        coEvery {
            testCodeService.findAllPackages()
        } returns NotFoundException().failure()

        authenticatedAs(idToken, staff) {
            get("/healthFormAutomaticTaskEngine/testCodePackages") { response ->
                assertThat(response).isNotFound()
            }
        }
    }
}
