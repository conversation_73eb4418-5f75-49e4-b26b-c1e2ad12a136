package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.Role
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.staff.client.HealthcareTeamFilters
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthcareTeamControllerV2Test : ControllerTestHelper() {

    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val staffService: StaffService = mockk()

    private val healthcareTeamControllerV2 = HealthcareTeamControllerV2(
        healthcareTeamService,
        staffService
    )

    private val uuid = RangeUUID.generate()
    private val date = LocalDateTime.now()
    private val physician = TestModelFactory.buildStaff()
    private val physicianId = physician.id

    private val address = TestModelFactory.buildStructuredAddress()
    private val healthcareTeam = HealthcareTeam(
        id = uuid,
        physicianStaffId = physicianId,
        createdAt = date,
        updatedAt = date,
        address = address
    )
    private val leanHealthcareTeamRequest = HealthcareTeamRequestV2(
        physicianStaffId = physicianId,
        type = HealthcareTeam.Type.LEAN,
        segment = HealthcareTeam.Segment.DEFAULT,
        maxMemberAssociation = 1000
    )
    private val leanHealthcareTeam = healthcareTeam.copy(
        type = HealthcareTeam.Type.LEAN,
        segment = HealthcareTeam.Segment.DEFAULT,
        maxMemberAssociation = 1000
    )
    private val leanHealthcareTeamResponse = HealthcareTeamResponseV2(
        id = leanHealthcareTeam.id,
        physicianStaffId = physicianId,
        type = HealthcareTeam.Type.LEAN,
        segment = HealthcareTeam.Segment.DEFAULT,
        description = physician.fullName,
        maxMemberAssociation = 1000,
        address = address
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { healthcareTeamControllerV2 }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        healthcareTeamService,
        staffService
    )

    @Test
    fun `#index returns all teams`() {
        val leanHealthcareTeam = leanHealthcareTeam.copy(id = RangeUUID.generate())

        val leanHealthcareTeamResponse = leanHealthcareTeamResponse.copy(id = leanHealthcareTeam.id)

        coEvery {
            healthcareTeamService.findBy(HealthcareTeamFilters(range = IntRange(0, 19)))
        } returns listOf(leanHealthcareTeam).success()

        coEvery {
            healthcareTeamService.countBy(HealthcareTeamFilters(range = IntRange(0, 19)))
        } returns 2.success()

        coEvery {
            staffService.findByList((leanHealthcareTeam.staffIds).distinct())
        } returns listOf(physician).success()

        authenticatedAs(idToken, staff) {
            get("/v2/healthcare-team") { response ->
                assertThat(response).isOKWithData(listOf(leanHealthcareTeamResponse))
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("2")
            }
        }

        coVerifyOnce { healthcareTeamService.findBy(any()) }
        coVerifyOnce { healthcareTeamService.countBy(any()) }
        coVerifyOnce { staffService.findByList(any()) }
    }

    @Test
    fun `#index returns teams by type`() {
        coEvery {
            healthcareTeamService.findBy(
                HealthcareTeamFilters(
                    type = HealthcareTeam.Type.LEAN,
                    range = IntRange(0, 19)
                )
            )
        } returns listOf(leanHealthcareTeam).success()

        coEvery {
            healthcareTeamService.countBy(
                HealthcareTeamFilters(
                    type = HealthcareTeam.Type.LEAN,
                    range = IntRange(0, 19)
                )
            )
        } returns 1.success()

        coEvery {
            staffService.findByList(leanHealthcareTeam.staffIds)
        } returns listOf(physician).success()

        authenticatedAs(idToken, staff) {
            get("/v2/healthcare-team?filter={\"type\":\"LEAN\"}") { response ->
                assertThat(response).isOKWithData(listOf(leanHealthcareTeamResponse))
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }

        coVerifyOnce { healthcareTeamService.findBy(any()) }
        coVerifyOnce { healthcareTeamService.countBy(any()) }
        coVerifyOnce { staffService.findByList(any()) }
    }

    @Test
    fun `#index returns teams by active`() {
        coEvery {
            healthcareTeamService.findBy(HealthcareTeamFilters(active = true, range = IntRange(0, 19)))
        } returns listOf(leanHealthcareTeam).success()

        coEvery {
            healthcareTeamService.countBy(HealthcareTeamFilters(active = true, range = IntRange(0, 19)))
        } returns 1.success()


        coEvery {
            staffService.findByList(leanHealthcareTeam.staffIds)
        } returns listOf(physician).success()

        authenticatedAs(idToken, staff) {
            get("/v2/healthcare-team?filter={\"active\":\"true\"}") { response ->
                assertThat(response).isOKWithData(listOf(leanHealthcareTeamResponse))
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }

        coVerifyOnce { healthcareTeamService.findBy(any()) }
        coVerifyOnce { healthcareTeamService.countBy(any()) }
        coVerifyOnce { staffService.findByList(any()) }
    }

    @Test
    fun `#index returns teams ids`() {
        coEvery {
            healthcareTeamService.findBy(HealthcareTeamFilters(ids = listOf(uuid)))
        } returns listOf(leanHealthcareTeam).success()

        coEvery {
            staffService.findByList(leanHealthcareTeam.staffIds)
        } returns listOf(physician).success()

        authenticatedAs(idToken, staff) {
            get("/v2/healthcare-team?filter={\"id\":[\"$uuid\"]}") { response ->
                assertThat(response).isOKWithData(listOf(leanHealthcareTeamResponse))
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }

        coVerifyOnce { healthcareTeamService.findBy(any()) }
        coVerifyOnce { staffService.findByList(any()) }
    }

    @Test
    fun `#get returns team`() {
        coEvery { healthcareTeamService.get(uuid, true) } returns leanHealthcareTeam.success()

        coEvery {
            staffService.findByList(leanHealthcareTeam.staffIds)
        } returns listOf(physician).success()


        authenticatedAs(idToken, staff) {
            get("/v2/healthcare-team/$uuid") { response ->
                assertThat(response).isOKWithData(leanHealthcareTeamResponse)
            }
        }

        coVerifyOnce { healthcareTeamService.get(any(), any()) }
        coVerifyOnce { staffService.findByList(any()) }
    }

    @Test
    fun `#create returns HealthcareTeam LEAN`() = mockRangeUUID(uuid) {
        mockLocalDateTime(date) {

            val leanHealthcareTeamToCreate = leanHealthcareTeam.copy(address = null)

            coEvery { healthcareTeamService.add(leanHealthcareTeamToCreate, true) } returns leanHealthcareTeam.success()
            coEvery {
                staffService.findByList(leanHealthcareTeam.staffIds)
            } returns listOf(physician).success()

            authenticatedAs(idToken, staff) {
                post(to = "/v2/healthcare-team", body = leanHealthcareTeamRequest) { response ->
                    assertThat(response).isOKWithData(leanHealthcareTeamResponse)
                }
            }

            coVerifyOnce { healthcareTeamService.add(any(), any()) }
            coVerifyOnce { staffService.findByList(any()) }
        }
    }

    @Test
    fun `#update HealthcareTeam LEAN`() = mockRangeUUID(uuid) {
        mockLocalDateTime(date) {
            val newPhysician = TestModelFactory.buildStaff()
            val newPhysicianId = newPhysician.id
            val leanHealthcareTeamRequest = leanHealthcareTeamRequest.copy(
                physicianStaffId = newPhysicianId,
                segment = HealthcareTeam.Segment.PEDIATRIC
            )
            val leanHealthcareTeamToUpdate =
                leanHealthcareTeam.copy(physicianStaffId = newPhysicianId, segment = HealthcareTeam.Segment.PEDIATRIC)
            val leanHealthcareTeamResponse = leanHealthcareTeamResponse.copy(
                physicianStaffId = newPhysicianId,
                segment = HealthcareTeam.Segment.PEDIATRIC
            )

            coEvery { healthcareTeamService.get(leanHealthcareTeam.id, true) } returns leanHealthcareTeam.success()
            coEvery {
                healthcareTeamService.update(
                    leanHealthcareTeamToUpdate,
                    true
                )
            } returns leanHealthcareTeamToUpdate.success()
            coEvery {
                staffService.findByList(leanHealthcareTeamToUpdate.staffIds)
            } returns listOf(newPhysician).success()

            authenticatedAs(idToken, staff) {
                put(to = "/v2/healthcare-team/${leanHealthcareTeam.id}", body = leanHealthcareTeamRequest) { response ->
                    assertThat(response).isOKWithData(leanHealthcareTeamResponse)
                }
            }

            coVerifyOnce { healthcareTeamService.get(any(), any()) }
            coVerifyOnce { healthcareTeamService.update(any(), any()) }
            coVerifyOnce { staffService.findByList(any()) }
        }
    }

    @Test
    fun `#getPhysicians returns all physicians`() {
        val physician1 = TestModelFactory.buildStaff()
        val physician2 = TestModelFactory.buildStaff()

        val expected = listOf(
            PhysicianResponse.from(physician1),
            PhysicianResponse.from(physician2)
        )

        coEvery {
            staffService
                .findActivesWithAnyRole(listOf(Role.MANAGER_PHYSICIAN))
        } returns listOf(physician1, physician2).success()

        authenticatedAs(idToken, staff) {
            get("/v2/healthcare-team/physicians") { response ->
                assertThat(response).isOKWithData(expected)
            }
        }

        coVerifyOnce { staffService.findActivesWithAnyRole(any()) }
        coVerify { healthcareTeamService wasNot called }
    }

}
