package br.com.alice.api.ops.controllers

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.models.AppointmentMacroRequest
import br.com.alice.appointment.client.AppointmentMacroService
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AppointmentMacro
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.mockkStatic
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.BeforeTest
import kotlin.test.Test

class AppointmentMacroControllerTest: ControllerTestHelper() {
    private val appointmentMacroService: AppointmentMacroService = mockk()
    private val controller = AppointmentMacroController(appointmentMacroService)
    private val appointmentMacro = TestModelFactory.buildAppointmentMacro()
    private val range = 0..10
    private val request = AppointmentMacroRequest(
        title = appointmentMacro.title,
        content = appointmentMacro.content,
        componentType = appointmentMacro.componentType
    )
    private val now = LocalDateTime.now()
    private val appointmentMacroIdGenerated = RangeUUID.generate()

    @BeforeTest
    override fun setup() {
        super.setup()

        clearMocks(
            appointmentMacroService,
        )

        module.single { controller }
    }

    @Test
    fun `#index returns macros list filtered by type and title`() {
        coEvery {
            appointmentMacroService.getByComponentTypesAndTitle(
                listOf(appointmentMacro.componentType),
                appointmentMacro.title,
                range
            )
        } returns listOf(appointmentMacro).success()

        coEvery {
            appointmentMacroService.countByComponentTypesAndTitle(
                listOf(appointmentMacro.componentType),
                appointmentMacro.title
            )
        } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/appointment_macro?filter={\"component_type\":\"${appointmentMacro.componentType}\", \"title\":\"${appointmentMacro.title}\"}&range=[0,10]") { response ->
                assertThat(response).isOKWithData(listOf(appointmentMacro))
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }

        coVerifyOnce { appointmentMacroService.getByComponentTypesAndTitle(any(), any(), any(), any()) }
        coVerifyOnce { appointmentMacroService.countByComponentTypesAndTitle(any(), any(), any()) }
    }

    @Test
    fun `#index returns macros list filtered by type and title when active is false`() {
        coEvery {
            appointmentMacroService.getByComponentTypesAndTitle(
                listOf(appointmentMacro.componentType),
                appointmentMacro.title,
                range,
                false
            )
        } returns emptyList<AppointmentMacro>().success()

        coEvery {
            appointmentMacroService.countByComponentTypesAndTitle(
                listOf(appointmentMacro.componentType),
                appointmentMacro.title
            )
        } returns 0.success()

        authenticatedAs(idToken, staff) {
            get("/appointment_macro?filter={\"component_type\":\"${appointmentMacro.componentType}\", \"title\":\"${appointmentMacro.title}\", \"active\":\"false\"}&range=[0,10]") { response ->
                assertThat(response).isOKWithData(emptyList<AppointmentMacro>())
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("0")
            }
        }

        coVerifyOnce { appointmentMacroService.getByComponentTypesAndTitle(any(), any(), any(), any()) }
        coVerifyOnce { appointmentMacroService.countByComponentTypesAndTitle(any(), any(), any()) }
    }

    @Test
    fun `#index returns macros list using range`() {
        coEvery {
            appointmentMacroService.getByComponentTypesAndTitle(
                null,
                null,
                range
            )
        } returns listOf(appointmentMacro).success()

        coEvery {
            appointmentMacroService.countByComponentTypesAndTitle(
                null,
                null
            )
        } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/appointment_macro?range=[0,10]") { response ->
                assertThat(response).isOKWithData(listOf(appointmentMacro))
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }

        coVerifyOnce { appointmentMacroService.getByComponentTypesAndTitle(any(), any(), any(), any()) }
        coVerifyOnce { appointmentMacroService.countByComponentTypesAndTitle(any(), any(), any()) }
    }

    @Test
    fun `#index returns error on get method`() {
        coEvery {
            appointmentMacroService.getByComponentTypesAndTitle(
                listOf(appointmentMacro.componentType),
                appointmentMacro.title,
                range
            )
        } returns Exception().failure()

        coEvery {
            appointmentMacroService.countByComponentTypesAndTitle(
                listOf(appointmentMacro.componentType),
                appointmentMacro.title
            )
        } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/appointment_macro?filter={\"component_type\":\"${appointmentMacro.componentType}\", \"title\":\"${appointmentMacro.title}\"}&range=[0,10]") { response ->
                assertThat(response).isInternalServerError()
            }
        }

        coVerifyOnce { appointmentMacroService.getByComponentTypesAndTitle(any(), any(), any(), any()) }
    }

    @Test
    fun `#get returns macro by id`() {
        coEvery {
            appointmentMacroService.get(
                appointmentMacro.id
            )
        } returns appointmentMacro.success()

        authenticatedAs(idToken, staff) {
            get("/appointment_macro/${appointmentMacro.id}") { response ->
                assertThat(response).isOKWithData(appointmentMacro)
            }
        }

        coVerifyOnce { appointmentMacroService.get(any()) }
    }

    @Test
    fun `#update returns macro updated`() {
        val appointMacro = appointmentMacro.copy(
            updatedAt = now,
            createdAt = now
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns now
            coEvery {
                appointmentMacroService.update(
                    appointMacro
                )
            } returns appointMacro.success()

            authenticatedAs(idToken, staff) {
                put("/appointment_macro/${appointmentMacro.id}", body = request) { response ->
                    assertThat(response).isOKWithData(appointMacro)
                }

                coVerifyOnce { appointmentMacroService.update(any()) }
            }
        }
    }

    @Test
    fun `#update returns macro updated with active false`() {
        val notActiveMacroRequest = request.copy(active = false)
        val appointMacro = appointmentMacro.copy(
            updatedAt = now,
            createdAt = now,
            active = false
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns now
            coEvery {
                appointmentMacroService.update(
                    appointMacro
                )
            } returns appointMacro.success()

            authenticatedAs(idToken, staff) {
                put("/appointment_macro/${appointmentMacro.id}", body = notActiveMacroRequest) { response ->
                    assertThat(response).isOKWithData(appointMacro)
                }

                coVerifyOnce { appointmentMacroService.update(any()) }
            }
        }
    }

    @Test
    fun `#create returns macro created`() {
        val appointMacro = appointmentMacro.copy(
            id = appointmentMacroIdGenerated,
            updatedAt = now,
            createdAt = now,
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns now
            mockkObject(RangeUUID) {
                coEvery { RangeUUID.generate() } returns appointmentMacroIdGenerated
                coEvery {
                    appointmentMacroService.create(
                        appointMacro
                    )
                } returns appointMacro.success()

                authenticatedAs(idToken, staff) {
                    post("/appointment_macro", body = request) { response ->
                        assertThat(response).isOKWithData(appointMacro)
                    }
                }

                coVerifyOnce { appointmentMacroService.create(any()) }
            }
        }
    }

    @Test
    fun `#create returns macro created when active is false`() {
        val notActiveMacroRequest = request.copy(active = false)
        val appointMacro = appointmentMacro.copy(
            id = appointmentMacroIdGenerated,
            updatedAt = now,
            createdAt = now,
            active = false
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns now
            mockkObject(RangeUUID) {
                coEvery { RangeUUID.generate() } returns appointmentMacroIdGenerated
                coEvery {
                    appointmentMacroService.create(
                        appointMacro
                    )
                } returns appointMacro.success()

                authenticatedAs(idToken, staff) {
                    post("/appointment_macro", body = notActiveMacroRequest) { response ->
                        assertThat(response).isOKWithData(appointMacro)
                    }
                }

                coVerifyOnce { appointmentMacroService.create(any()) }
            }
        }
    }

    @Test
    fun `#delete returns macro soft deleted`() {
        val appointMacro = appointmentMacro.copy(
            updatedAt = now,
        )

        mockkStatic(LocalDateTime::class) {
            every { LocalDateTime.now() } returns now
                coEvery {
                    appointmentMacroService.delete(
                        appointMacro.id
                    )
                } returns appointMacro.copy(active = false).success()

                authenticatedAs(idToken, staff) {
                    delete("/appointment_macro/${appointmentMacro.id}") { response ->
                        assertThat(response).isOKWithData(appointMacro.copy(active = false))
                    }
                }

                coVerifyOnce { appointmentMacroService.delete(any()) }
            }
    }
}
