package br.com.alice.api.ops.controllers.healthlogics

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.common.RangeUUID
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.ServiceScriptAction
import br.com.alice.healthlogic.client.HealthLogicsService
import br.com.alice.healthlogic.models.healthLogics.HealthLogic
import br.com.alice.healthlogic.models.healthLogics.HealthLogicProgram
import br.com.alice.healthlogic.models.healthLogics.HealthLogicProgramLite
import br.com.alice.healthlogic.models.healthLogics.HealthLogicProgramUpsertRequest
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

internal class HealthLogicsControllerTest : ControllerTestHelper() {
    private val healthLogicsService: HealthLogicsService = mockk()

    private val controller = HealthLogicsController(
        healthLogicsService
    )

    private val healthLogicId = RangeUUID.generate()
    private val healthLogicRequest = HealthLogicRequest(
        name = "Saúde mental",
        active = true
    )

    private val healthLogic = HealthLogic(
        id = healthLogicId,
        name = "Saúde mental",
        active = true
    )

    private val program = HealthLogicProgram(
        name = "Leve",
        id = RangeUUID.generate(),
        priority = 1
    )

    private val programLite = HealthLogicProgramLite(
        name = "Leve",
        id = RangeUUID.generate(),
        priority = 1
    )

    private val action = TestModelFactory.buildServiceScriptAction()

    private val programRequest = HealthLogicProgramUpsertRequest(
        name = program.name,
        active = true,
        actionIds = emptyList(),
        priority = 1
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearAllMocks()

        module.single { controller }
    }

    @Test
    fun `#index calls service and returns health logic`() {
        coEvery {
            healthLogicsService.findHealthLogics("HealthLogic", true, IntRange(0,9))
        } returns listOf(healthLogic).success()
        coEvery {
            healthLogicsService.countHealthLogics("HealthLogic", true)
        } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/healthLogics?range=[0,9]&filter={q:HealthLogic,active:true}") { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<List<HealthLogic>>()

                assertThat(parsedResponse).isEqualTo(listOf(healthLogic))
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }
    }

    @Test
    fun `#index calls service and returns health logic - no filters`() {
        coEvery {
            healthLogicsService.findHealthLogics(null, null, IntRange(0,19))
        } returns listOf(healthLogic).success()
        coEvery {
            healthLogicsService.countHealthLogics(null, null)
        } returns 1.success()

        authenticatedAs(idToken, staff) {
            get("/healthLogics") { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<List<HealthLogic>>()

                assertThat(parsedResponse).isEqualTo(listOf(healthLogic))
                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("1")
            }
        }
    }

    @Test
    fun `#getHealthLogic calls service and returns health logic`() {
        coEvery {
            healthLogicsService.get(healthLogicId)
        } returns healthLogic.success()

        authenticatedAs(idToken, staff) {
            get("/healthLogics/$healthLogicId") { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<HealthLogic>()

                assertThat(parsedResponse).isEqualTo(healthLogic)
            }
        }
    }

    @Test
    fun `#createHealthLogic calls service and returns created health logic`() {
        coEvery {
            healthLogicsService.create(
                name = healthLogicRequest.name,
                active = true
            )
        } returns healthLogic.success()

        authenticatedAs(idToken, staff) {
            post("/healthLogics", healthLogicRequest) { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<HealthLogic>()

                assertThat(parsedResponse).isEqualTo(healthLogic)
            }
        }
    }

    @Test
    fun `#updateHealthLogic calls service and returns created health logic`() {
        coEvery {
            healthLogicsService.update(
                id = healthLogicId,
                name = healthLogicRequest.name,
                active = true
            )
        } returns healthLogic.success()

        authenticatedAs(idToken, staff) {
            put("/healthLogics/$healthLogicId", healthLogicRequest) { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<HealthLogic>()

                assertThat(parsedResponse).isEqualTo(healthLogic)
            }
        }
    }

    @Test
    fun `#deleteHealthLogic calls service and returns success`() {
        coEvery {
            healthLogicsService.delete(
                id = healthLogicId
            )
        } returns true.success()

        authenticatedAs(idToken, staff) {
            put("/healthLogics/$healthLogicId/delete") { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#createProgram calls service and returns created program`() {
        coEvery {
            healthLogicsService.createProgram(
                healthLogicId = healthLogicId,
                request = programRequest
            )
        } returns program.success()

        authenticatedAs(idToken, staff) {
            post("/healthLogics/$healthLogicId/program", programRequest) { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<HealthLogicProgram>()

                assertThat(parsedResponse).isEqualTo(program)
            }
        }
    }

    @Test
    fun `#updateProgram calls service and returns updated program`() {

        coEvery {
            healthLogicsService.updateProgram(
                healthLogicId = healthLogicId,
                programId = program.id,
                request = programRequest
            )
        } returns program.success()

        authenticatedAs(idToken, staff) {
            put("/healthLogics/$healthLogicId/program/${program.id}", programRequest) { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<HealthLogicProgram>()

                assertThat(parsedResponse).isEqualTo(program)
            }
        }
    }

    @Test
    fun `#deleteProgram calls service and returns success`() {
        coEvery {
            healthLogicsService.deleteProgram(
                healthLogicId = healthLogicId,
                programId = program.id
            )
        } returns true.success()

        authenticatedAs(idToken, staff) {
            put("/healthLogics/$healthLogicId/program/${program.id}/delete") { response ->
                assertThat(response).isOK()
            }
        }
    }

    @Test
    fun `#getPrograms calls service and returns created program`() {
        coEvery {
            healthLogicsService.findPrograms(healthLogicId = healthLogicId)
        } returns listOf(programLite).success()

        authenticatedAs(idToken, staff) {
            get("/healthLogics/$healthLogicId/program") { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<List<HealthLogicProgramLite>>()

                assertThat(parsedResponse).isEqualTo(listOf(programLite))
            }
        }
    }

    @Test
    fun `#getProgramDetails calls service and returns program`() {
        coEvery {
            healthLogicsService.getProgramDetails(healthLogicId = healthLogicId, healthLogicProgramId = program.id)
        } returns program.success()

        authenticatedAs(idToken, staff) {
            get("/healthLogics/$healthLogicId/program/${program.id}") { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<HealthLogicProgram>()

                assertThat(parsedResponse).isEqualTo(program)
            }
        }
    }

    @Test
    fun `#getOrCreateAction calls service and returns action`() {
        val request = ActionRequest(
            type = action.type,
            externalId = action.externalId
        )
        coEvery {
            healthLogicsService.getOrCreateAction(request.type, request.externalId)
        } returns action.success()

        authenticatedAs(idToken, staff) {
            post("/healthLogics/actions", request) { response ->
                assertThat(response).isSuccessfulJson()

                val parsedResponse = response.bodyAsJson<ServiceScriptAction>()

                assertThat(parsedResponse).isEqualTo(action)
            }
        }
    }
}
