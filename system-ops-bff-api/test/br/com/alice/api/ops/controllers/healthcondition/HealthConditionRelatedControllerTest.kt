package br.com.alice.api.ops.controllers.healthcondition

import br.com.alice.api.ops.ControllerTestHelper
import br.com.alice.api.ops.models.HealthConditionRelatedRequest
import br.com.alice.api.ops.models.HealthConditionRelatedResponse
import br.com.alice.common.RangeUUID
import br.com.alice.common.convertTo
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.helpers.mockLocalDateTime
import br.com.alice.common.helpers.mockRangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthConditionCodeType
import br.com.alice.healthcondition.client.HealthConditionRelatedService
import br.com.alice.healthcondition.client.HealthConditionService
import com.github.kittinunf.result.success
import io.ktor.http.HttpHeaders
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.confirmVerified
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import java.time.LocalDateTime
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthConditionRelatedControllerTest : ControllerTestHelper() {

    private val healthConditionRelatedService: HealthConditionRelatedService = mockk()
    private val healthConditionService: HealthConditionService = mockk()
    private val healthConditionRelatedController = HealthConditionRelatedController(
        healthConditionRelatedService,
        healthConditionService
    )

    private val uuid = RangeUUID.generate()
    private val now = LocalDateTime.now()
    private val ciap = TestModelFactory.buildHealthCondition(codeType = HealthConditionCodeType.CIAP_2)
    private val cipe = TestModelFactory.buildHealthCondition(
        code = "10000000",
        codeType = HealthConditionCodeType.CIPE
    )
    private val healthConditionIds = listOf(cipe.id, ciap.id)
    private val healthConditions = listOf(ciap, cipe)
    private val healthConditionRelated = TestModelFactory.buildHealthConditionRelated(
        healthConditionId = ciap.id,
        healthConditionIds = listOf(cipe.id),
        createdAt = now,
        updatedAt = now
    )
    private val request = healthConditionRelated.convertTo(HealthConditionRelatedRequest::class)
    private val responseModel = HealthConditionRelatedResponse(
        id = healthConditionRelated.id,
        healthCondition = ciap,
        healthConditions = listOf(cipe),
        type = HealthConditionCodeType.CIPE
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        module.single { healthConditionRelatedController }
    }

    @AfterTest
    fun confirmMocks() = confirmVerified(
        healthConditionRelatedService,
        healthConditionService
    )

    @Test
    fun `#create should create HealthConditionRelated`() = mockRangeUUID(uuid) {
        mockLocalDateTime(now) {
            authenticatedAs(idToken, staff) {
                coEvery {
                    healthConditionRelatedService.add(healthConditionRelated.copy(id = uuid))
                } returns healthConditionRelated.success()

                coEvery { healthConditionService.getAll(healthConditionIds) } returns healthConditions.success()

                post(to = "/health_condition_related", body = request) { response ->
                    assertThat(response).isOKWithData(responseModel)
                }

                coVerifyOnce { healthConditionRelatedService.add(any()) }
                coVerifyOnce { healthConditionService.getAll(any()) }
            }
        }
    }

    @Test
    fun `#index should get HealthConditionRelated by range`() {
        val healthConditionRelated2 = healthConditionRelated.copy(id = RangeUUID.generate())
        val responseModel2 = responseModel.copy(id = healthConditionRelated2.id)

        coEvery {
            healthConditionRelatedService.getByRange(0..49)
        } returns listOf(healthConditionRelated, healthConditionRelated2).success()
        coEvery { healthConditionRelatedService.count() } returns 10.success()
        coEvery { healthConditionService.getAll(healthConditionIds) } returns healthConditions.success()

        authenticatedAs(idToken, staff) {
            get("/health_condition_related?filter={}&range=[0,49]") { response ->
                assertThat(response).isOKWithData(listOf(responseModel, responseModel2))

                assertThat(response.headers[HttpHeaders.ContentRange]).isEqualTo("10")
            }
        }

        coVerifyOnce { healthConditionRelatedService.getByRange(any()) }
        coVerifyOnce { healthConditionRelatedService.count() }
        coVerifyOnce { healthConditionService.getAll(any()) }
    }

    @Test
    fun `#index should get HealthConditionRelated by query`() {
        authenticatedAs(idToken, staff) {
            get("/health_condition_related?filter={\"q\":\"somecode\"}") { response ->
                assertThat(response).isBadRequest()
            }
        }

        coVerify { healthConditionRelatedService wasNot called }
    }

    @Test
    fun `#getById should get HealthConditionRelated by id`() {
        coEvery { healthConditionRelatedService.get(healthConditionRelated.id) } returns healthConditionRelated.success()
        coEvery { healthConditionService.getAll(healthConditionIds) } returns healthConditions.success()

        authenticatedAs(idToken, staff) {
            get("/health_condition_related/${healthConditionRelated.id}") { response ->
                assertThat(response).isOKWithData(responseModel)
            }
        }

        coVerifyOnce { healthConditionRelatedService.get(any()) }
        coVerifyOnce { healthConditionService.getAll(any()) }
    }

    @Test
    fun `#update should update HealthConditionRelated`() = mockLocalDateTime(now) {
        val ciap = ciap.copy(id = uuid)
        val healthConditionRelatedUpdated = healthConditionRelated.copy(healthConditionId = uuid)
        val request = request.copy(healthConditionId = ciap.id)
        val responseModel = responseModel.copy(healthCondition = ciap)

        coEvery { healthConditionRelatedService.get(healthConditionRelated.id) } returns healthConditionRelated.success()
        coEvery {
            healthConditionRelatedService.update(healthConditionRelatedUpdated)
        } returns healthConditionRelatedUpdated.success()
        coEvery { healthConditionService.getAll(listOf(cipe.id, ciap.id)) } returns listOf(ciap, cipe).success()

        authenticatedAs(idToken, staff) {
            put(to = "/health_condition_related/${healthConditionRelated.id}", body = request) { response ->
                assertThat(response).isOKWithData(responseModel)
            }
        }

        coVerifyOnce { healthConditionRelatedService.get(any()) }
        coVerifyOnce { healthConditionRelatedService.update(any()) }
        coVerifyOnce { healthConditionService.getAll(any()) }
    }

}
