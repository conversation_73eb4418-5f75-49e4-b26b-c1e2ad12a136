plugins {
    kotlin
    application
    id("com.github.johnrengelman.shadow")
    id("com.google.cloud.tools.jib")
    id("org.sonarqube")
}

group = "br.com.alice.business-domain-service"
version = aliceBusinessDomainServiceVersion

application {
    mainClass.set("io.ktor.server.netty.EngineMain")
}

sourceSets {
    main {
        kotlin.sourceDirs = files("src", "${layout.buildDirectory}/generated/source/kotlin")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

sonarqube {
    properties {
        property("sonar.projectKey", "mono:business-domain-service")
        property("sonar.organization", "alice-health")
        property("sonar.host.url", "https://sonarcloud.io")
    }
}

tasks {
    shadowJar {
        isZip64 = true
    }
}

dependencies {
    implementation(project(":business-domain-client"))
    implementation(project(":common"))
    implementation(project(":common-kafka"))
    implementation(project(":common-redis"))
    implementation(project(":communication"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:product-domain-service-data-package"))
	implementation(project(":data-packages:membership-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:clinical-account-domain-service-data-package"))
	implementation(project(":data-packages:appointment-domain-service-data-package"))
	implementation(project(":data-packages:schedule-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-data-package"))
    implementation(project(":data-packages:business-domain-service-model-package"))
	implementation(project(":data-packages:money-in-domain-service-data-package"))
	implementation(project(":data-packages:amas-domain-service-data-package"))
	implementation(project(":data-packages:file-vault-service-data-package"))
    implementation(project(":data-packages:health-condition-domain-service-data-package"))
    implementation(project(":data-packages:staff-domain-service-data-package"))
    implementation(project(":data-packages:sales-channel-domain-service-data-package"))
    implementation(project(":data-packages:wanda-domain-service-data-package"))
    implementation(project(":clinical-account-domain-client"))
    implementation(project(":feature-config-domain-client"))
    implementation(project(":file-vault-client"))
    implementation(project(":membership-domain-client"))
    implementation(project(":money-in-domain-client"))
    implementation(project(":person-domain-client"))
    implementation(project(":schedule-domain-client"))
    implementation(project(":staff-domain-client"))
    implementation(project(":wanda-domain-client"))
    implementation(project(":product-domain-client"))
    implementation(project(":sales-channel-domain-client"))
    implementation(project(":nullvs-integration-client"))
    implementation(project(":ehr-domain-client"))
    implementation(project(":appointment-domain-client"))
    implementation(platform("software.amazon.awssdk:bom:$awsSdkVersion"))
    implementation("io.github.resilience4j:resilience4j-circuitbreaker:$resilience4jVersion")

    implementation("software.amazon.awssdk:core:$awsSdkVersion")
    implementation("software.amazon.awssdk:auth:$awsSdkVersion")
    implementation("software.amazon.awssdk:ses")
    ktor2Dependencies()
    implementation("io.ktor:ktor-client-apache5:$ktor2Version")

    testImplementation(project(":data-layer-common-tests"))
    testImplementation(project(":common-tests"))
    testImplementation("org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlinCoroutinesVersion")

    implementation("org.apache.poi:poi:5.2.3")
    implementation("org.apache.poi:poi-ooxml:5.2.3")

    test2Dependencies()
}
