package br.com.alice.staff.converters

import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.HealthProfessional
import br.com.alice.data.layer.models.Staff
import br.com.alice.staff.models.SimpleStaffResponse
import br.com.alice.staff.models.SimpleStaffSpecialtyResponse
import br.com.alice.staff.models.StaffPermissionType
import br.com.alice.staff.models.StaffPermissions

object SimpleStaffResponseConverter : Converter<Staff, SimpleStaffResponse>(
    Staff::class, SimpleStaffResponse::class
) {
    fun convert(
        source: Staff,
        healthProfessional: HealthProfessional? = null,
        medicalSpecialty: SimpleStaffSpecialtyResponse? = null
    ): SimpleStaffResponse =
        convert(
            source,
            map(SimpleStaffResponse::name) from source.firstName,
            map(SimpleStaffResponse::photoURL) from source.profileImageUrl,
            map(SimpleStaffResponse::permissions) from buildPermissions(source),
            map(SimpleStaffResponse::roles) from source.allRoles(),
            map(SimpleStaffResponse::allowedToOnCall) from source.isAliceHealthProfessionalOrNavigator(),
            map(SimpleStaffResponse::onCall) from (healthProfessional?.onCall ?: false),
            map(SimpleStaffResponse::specialty) from medicalSpecialty
        )

    private fun buildPermissions(staff: Staff): Map<String, Any> {
        val roles = staff.allRoles()

        val createAppointments = StaffPermissions.appointmentRoles.mapNotNull { appointmentRole ->
            takeIf { roles.any { appointmentRole.value.contains(it) } }?.let { appointmentRole.key.name }
        }

        return mapOf<String, Any>()
            .plusSafe(StaffPermissionType.PUBLISH_PRESCRIPTION, staff.isAllowedToPublishPrescriptions())
            .plusSafe(StaffPermissionType.GET_CHAT, staff.isAllowedToGetChat())
            .plusSafe(StaffPermissionType.VIEW_CHANNELS_DASHBOARD, isAllowedToViewChannelsDashboard(staff))
            .plusSafe(StaffPermissionType.HAS_CHIEF_ROLE, hasChiefRole(staff))
            .plusSafe(StaffPermissionType.EDIT_CPTS, staff.canEditCPTS())
            .plusSafe(StaffPermissionType.CREATE_APPOINTMENT, createAppointments)
    }

    private fun Staff.canEditCPTS() = isRiskNurse() || isIntermittentRiskNurse() || isMedRisk() || isChiefRisk()

    private fun isAllowedToViewChannelsDashboard(staff: Staff) =
        staff.isAliceHealthProfessionalOrNavigator() || staff.isProductTech()

    private fun hasChiefRole(staff: Staff) = staff.isChief()

    private fun Map<String, Any>.plusSafe(key: StaffPermissionType, value: Any) =
        when (value) {
            is List<*> -> {
                if (value.isNotEmpty()) this.plus(key.name.lowercase() to value)
                else this
            }

            else -> this.plus(key.name.lowercase() to value)
        }
}
