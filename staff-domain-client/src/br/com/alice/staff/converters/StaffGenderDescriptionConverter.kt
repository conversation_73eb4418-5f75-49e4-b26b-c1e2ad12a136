package br.com.alice.staff.converters

import br.com.alice.common.core.Role
import br.com.alice.common.models.Gender
import br.com.alice.data.layer.models.Staff

object StaffGenderDescriptionConverter {

    val physicianOptions = listOf("médica", "médico", "medicina")

    fun convert(source: Staff) = when {
        source.isDigitalCareNurse() -> convert(Role.DIGITAL_CARE_NURSE, source.gender)
        source.isNavigator() -> convert(Role.NAVIGATOR, source.gender)
        source.isNutritionist() -> convert(Role.NUTRITIONIST, source.gender)
        source.isNurseOrNavigator() -> convert(Role.SCREENING_NURSE, source.gender)
        source.isPhysician() -> convert(Role.DIGITAL_CARE_PHYSICIAN, source.gender)
        source.isPhysicalEducator() -> convert(Role.PHYSICAL_EDUCATOR, source.gender)
        source.isCommunity() -> convert(Role.COMMUNITY, source.gender)
        else -> "atendimento alice"
    }

    fun convert(role: Role, gender: Gender) = when (role) {
        Role.CHIEF_PHYSICIAN -> when (gender) {
            Gender.FEMALE -> "médica"
            Gender.MALE -> "médico"
            else -> "medicina"
        }
        Role.NUTRITIONIST -> "nutricionista"
        Role.PSYCHOLOGIST -> when (gender) {
            Gender.FEMALE -> "psicóloga"
            Gender.MALE -> "psicólogo"
            else -> "psicologia"
        }
        Role.COMMUNITY -> "especialista"
        Role.MANAGER_PHYSICIAN,
        Role.DIGITAL_CARE_PHYSICIAN,
        Role.ON_SITE_PHYSICIAN,
        Role.CHIEF_DIGITAL_CARE_PHYSICIAN -> when (gender) {
            Gender.FEMALE -> "médica"
            Gender.MALE -> "médico"
            else -> "medicina"
        }
        Role.HEALTHCARE_TEAM_NURSE,
        Role.DIGITAL_CARE_NURSE,
        Role.DIGITAL_SCREENING_NURSE,
        Role.CHIEF_DIGITAL_CARE_NURSE,
        Role.SCREENING_NURSE,
        Role.ON_SITE_NURSE -> when (gender) {
            Gender.FEMALE -> "enfermeira"
            Gender.MALE -> "enfermeiro"
            else -> "enfermagem"
        }
        Role.PHYSICAL_EDUCATOR -> when (gender) {
            Gender.FEMALE -> "preparadora física"
            Gender.MALE -> "preparador físico"
            else -> "preparação física"
        }
        Role.OBSTETRICIAN -> "obstetriz"
        Role.NAVIGATOR -> "atendimento alice"
        else -> "profissional de saúde"
    }
}
