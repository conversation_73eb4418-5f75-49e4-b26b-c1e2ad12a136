package br.com.alice.testresult.client

import br.com.alice.common.core.PersonId
import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import br.com.alice.data.layer.models.DischargeSummary
import com.github.kittinunf.result.Result
import java.util.UUID

@RemoteService
interface DischargeSummaryService : Service {
    override val namespace: String get() = "test_results"
    override val serviceName: String get() = "discharge_summary"

    suspend fun upsert(model: DischargeSummary): Result<DischargeSummary, Throwable>
    suspend fun findById(id: UUID): Result<DischargeSummary, Throwable>
    suspend fun findByPersonId(personId: PersonId): Result<List<DischargeSummary>, Throwable>
}
