package br.com.alice.testresult.models

import br.com.alice.common.core.extensions.toCustomFormat
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.data.layer.models.ProviderName
import java.time.LocalDateTime

data class AliceCommonTestResultFile(
    val description: String = "Resultado de Exame",
    val performedAt: LocalDateTime,
    val id: String,
    val integrationSource: ProviderIntegration? = null,
    val realizedAtProvider: ProviderName? = null,
    val bundleItems: List<AliceCommonTestResultFileItem>? = emptyList()
) {
    fun toKey(): AliceCommonKeyCondition =
        AliceCommonKeyCondition(
            date = this.performedAt.toCustomFormat("dd/MM/yyyy"),
            realizedAtProvider = this.realizedAtProvider ?: this.integrationSource?.toProviderName()
        )
}

data class AliceCommonTestResultFileItem(val id: String, val description: String)

data class AliceCommonKeyCondition(val date: String, val realizedAtProvider: ProviderName?)
