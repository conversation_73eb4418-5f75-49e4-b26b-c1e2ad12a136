package br.com.alice.testresult.models

interface BaseExamResultData {
    val id: String
    val description: String
    val attachmentUrl: String
}

data class ProviderData(
    val name: String,
    val description: String,
    val logo: String,
)

data class ExamResult(
    val date: String,
    val provider: ProviderData?,
    val results: List<ExamResultBundle>
)

data class ExamResultBundle(
    override val id: String,
    override val description: String,
    override val attachmentUrl: String,
    val items: List<ExamResultItem>
) : BaseExamResultData

data class ExamResultItem(
    override val id: String,
    override val description: String,
    override val attachmentUrl: String
) : BaseExamResultData
