package br.com.alice.testresult.exceptions

import br.com.alice.common.core.exceptions.InvalidArgumentException
import br.com.alice.data.layer.models.AliceTestResultType
import br.com.alice.data.layer.models.ProviderIntegration

class InvalidRequestProviderException(
    message: String,
    code: String = "invalid_request_provider",
    cause: Throwable? = null
) : InvalidArgumentException(message, code, cause) {
    constructor(wrongProvider: ProviderIntegration) : this(message = "Invalid Request Provider: $wrongProvider")
}

class InvalidRequestResultTypeException(
    message: String,
    code: String = "invalid_request_result_type_provider",
    cause: Throwable? = null
) : InvalidArgumentException(message, code, cause) {
    constructor(wrongType: AliceTestResultType) : this(message = "Invalid Request Result Type: $wrongType")
}
