package br.com.alice.testresult.events

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.DischargeSummary
import br.com.alice.testresult.SERVICE_NAME
import java.util.UUID

data class DischargeSummaryCardEvent(
    private val summary: DischargeSummary,
    private val tertiaryId: UUID?,
) : NotificationEvent<DischargeSummaryPayload>(
    name = name,
    producer = SERVICE_NAME,
    payload = DischargeSummaryPayload(summary, tertiaryId)
) {
    companion object {
        const val name = "discharge-summary-card"
    }
}

data class DischargeSummaryPayload(
    val summary: DischargeSummary,
    val tertiaryId: UUID?
) {
    fun hasLink() = this.tertiaryId != null
}
