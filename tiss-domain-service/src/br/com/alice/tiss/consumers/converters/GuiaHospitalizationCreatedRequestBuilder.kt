package br.com.alice.tiss.consumers.converters

import br.com.alice.common.MvUtil
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.extensions.coResultOf
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.AnsAttendanceRegime
import br.com.alice.data.layer.models.AnsRegisterCode
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HospitalizationInfoService
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.DEFAULT_CBO
import br.com.alice.exec.indicator.models.DEFAULT_COUNCIL_NUMBER
import br.com.alice.exec.indicator.models.DEFAULT_PROFESSIONAL_NAME
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.tiss.models.CreateGuiaHospitalizationRequest
import com.github.kittinunf.result.getOrNull
import java.time.LocalDate

class GuiaHospitalizationCreatedRequestBuilder(
    private val healthcareResourceService: HealthcareResourceService,
    private val hospitalizationInfoService: HospitalizationInfoService,
    private val providerUnitService: ProviderUnitService
) {
    companion object {
        private const val DEFAULT_CID = "Z00"
        private const val DEFAULT_AUTHORIZATION_TYPE = "1"
        private const val DEFAULT_CLINICAL_INDICATION = "Solicitacao internacao"
    }

    suspend fun build(
        procedures: List<MvAuthorizedProcedure>,
        totvsGuia: TotvsGuia,
        beneficiary: Beneficiary,
        origin: GuiaOrigin
    ) = coResultOf<CreateGuiaHospitalizationRequest, Throwable> {
        val firstProcedure = procedures.first()
        val procedureCodes = procedures.mapNotNull { it.procedureId }
        val healthcareResources = healthcareResourceService.findByCodes(procedureCodes).get()
        val healthcareResourcesByCode = healthcareResources.associateBy { it.code }
        val hospitalizationInfo = hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id).getOrNull()
            ?: throw IllegalArgumentException("Hospitalization info not found")
        val providerUnit = providerUnitService.get(hospitalizationInfo.providerUnitId).get()
        val requesterData = RequesterData(name = providerUnit.name.unaccent(), providerCode = providerUnit.cnpj!!)

        CreateGuiaHospitalizationRequest(
            guiaNumber = totvsGuia.code,
            requestDate = LocalDate.now(),
            beneficiary = Beneficiary(
                name = beneficiary.name?.unaccent(),
                nationalId = beneficiary.nationalId,
                newBornAttendance = firstProcedure.extraGuiaInfo.newBorn ?: false
            ),
            procedures = procedures.map { procedure ->
                GuiaProcedure(
                    code = procedure.procedureId!!,
                    description = healthcareResourcesByCode[procedure.procedureId!!]?.description ?: "NO DESCRIPTION",
                    quantity = procedure.extraGuiaInfo.quantity ?: 1,
                    status = procedure.status,
                    table = healthcareResourcesByCode[procedure.procedureId!!]?.tableType ?: "98"
                )
            },
            requester = requesterData,
            professional = Professional(
                name = firstProcedure.requestedByProfessional.fullName?.unaccent().nullIfBlank()
                    ?: DEFAULT_PROFESSIONAL_NAME,
                councilNumber = firstProcedure.requestedByProfessional.councilNumber.nullIfBlank()
                    ?: DEFAULT_COUNCIL_NUMBER,
                council = firstProcedure.requestedByProfessional.council,
                councilState = firstProcedure.requestedByProfessional.councilState,
                phoneNumber = firstProcedure.requestedByProfessional.phone?.onlyNumbers(),
                email = firstProcedure.requestedByProfessional.email,
                cbo = firstProcedure.requestedByProfessional.cboCode ?: DEFAULT_CBO,
            ),
            guiaExternalCode = totvsGuia.externalCode,
            origin = origin,
            attendanceRegime = firstProcedure.extraGuiaInfo.procedureType.toAnsAttendanceRegime(),
            accidentIndication = firstProcedure.extraGuiaInfo.accidentIndication.toAnsAccidentIndication(),
            admissionType = hospitalizationInfo.type.code,
            admissionDaysRequested = hospitalizationInfo.numberOfDays.toBigInteger(),
            opmeIndicator = hospitalizationInfo.opmeIndication,
            quimioterapyIndicator = hospitalizationInfo.chemotherapyIndication,
            clinicalIndication = hospitalizationInfo.clinicalIndication.sanitizeField(),
            diagnosisCID = hospitalizationInfo.healthCondition.code ?: DEFAULT_CID,
            clinicalAttachment = null,
            recommendedAdmissionDate = hospitalizationInfo.suggestedDate,
            ansCode = AnsRegisterCode.ALICE.code,
            authorizationType = DEFAULT_AUTHORIZATION_TYPE,
            attendanceCharacter = hospitalizationInfo.attendanceCharacter.name,
        )
    }

    private fun MvUtil.TISS.toAnsAttendanceRegime() =
        when (this) {
            MvUtil.TISS.HOSPITALIZATION -> AnsAttendanceRegime.HOSPITALIZATION.code
            MvUtil.TISS.PS -> AnsAttendanceRegime.EMERGENCY_ROOM.code
            else -> AnsAttendanceRegime.AMBULATORY.code
        }

    private fun String?.toAnsAccidentIndication() = runCatching {
        if (this == null) AnsAccidentIndication.NOT_ACCIDENT.code
        else AnsAccidentIndication.valueOf(this).code
    }.getOrElse { AnsAccidentIndication.NOT_ACCIDENT.code }

    private fun String?.sanitizeField() =
        this?.unaccent()?.uppercase()?.onlyDigits()?.trim()?.nullIfBlank() ?: "Not informed"
}
