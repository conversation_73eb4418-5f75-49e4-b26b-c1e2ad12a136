package br.com.alice.tiss.consumers.converters

import br.com.alice.common.core.exceptions.RequiredFieldException
import br.com.alice.common.core.extensions.nullIfBlank
import br.com.alice.common.core.extensions.onlyDigits
import br.com.alice.common.core.extensions.onlyNumbers
import br.com.alice.common.core.extensions.unaccent
import br.com.alice.common.models.Sex
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.AnsAttendanceRegime
import br.com.alice.data.layer.models.AnsAuthorizationType
import br.com.alice.data.layer.models.AnsRegisterCode
import br.com.alice.data.layer.models.AttachmentChemotherapy
import br.com.alice.data.layer.models.AttendanceCharacter
import br.com.alice.data.layer.models.MvAuthorizedProcedure
import br.com.alice.data.layer.models.Person
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.TotvsGuia
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.DEFAULT_CBO
import br.com.alice.exec.indicator.models.DEFAULT_COUNCIL_NUMBER
import br.com.alice.exec.indicator.models.DEFAULT_PROFESSIONAL_NAME
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.provider.client.ProviderUnitService
import br.com.alice.tiss.exceptions.InvalidSexFieldException
import br.com.alice.tiss.models.AttachmentHeader
import br.com.alice.tiss.models.BeneficiaryAdditionalData
import br.com.alice.tiss.models.ChemotherapyOncologicalDiagnosis
import br.com.alice.tiss.models.ChemotherapyRequestAttachment
import br.com.alice.tiss.models.CreateGuiaRequest
import br.com.alice.tiss.models.OncologicalDiagnosis
import br.com.alice.tiss.models.RequestedDrug
import br.com.alice.tiss.models.RequestedDrugs
import com.github.kittinunf.result.Result
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.math.BigDecimal
import java.math.MathContext
import java.math.RoundingMode
import java.time.LocalDate
import kotlin.math.sqrt

class GuiaWithChemotherapyCreatedRequestBuilder(
    private val providerUnitService: ProviderUnitService,
    private val healthcareResourceService: HealthcareResourceService
) {

    companion object {
        private const val DEFAULT_CID = "Z00"
    }

    suspend fun build(
        attachment: AttachmentChemotherapy,
        totvsGuia: TotvsGuia,
        chemotherapyTotvsGuia: TotvsGuia,
        beneficiary: Beneficiary,
        origin: GuiaOrigin,
        person: Person,
        referenceGuiaProcedures: List<MvAuthorizedProcedure>,
    ): Result<Pair<ChemotherapyRequestAttachment, CreateGuiaRequest>, Throwable> {
        val procedureCodes = referenceGuiaProcedures.mapNotNull { it.procedureId }
        val healthcareResources = healthcareResourceService.findByCodes(procedureCodes).get()
        val healthcareResourcesByCode = healthcareResources.associateBy { it.code }

        val chemotherapyRequestAttachment = ChemotherapyRequestAttachment(
            providerCnpj = totvsGuia.cnpj!!,
            origin = origin,
            attachmentHeader = AttachmentHeader(
                ansCode = AnsRegisterCode.ALICE.code,
                attachmentGuideNumber = chemotherapyTotvsGuia.code,
                referencedGuideNumber = chemotherapyTotvsGuia.code,
                requestDate = LocalDate.now()
            ),
            professional = toProfessional(totvsGuia.requestedByProfessional),
            beneficiaryData = beneficiary,
            beneficiaryAdditionalData = BeneficiaryAdditionalData(
                weight = attachment.weight,
                height = attachment.height,
                bodySurface = bodySurface(attachment.height, attachment.weight),
                age = person.age.toBigInteger(),
                sex = person.sex?.toSexTuss()!!,
            ),
            chemotherapyOncologicalDiagnosis = attachment.chemotherapyOncologicalDiagnosis.toChemotherapyOncologicalDiagnosis(),
            requestedDrugs = attachment.requestedDrugs.toRequestedDrugs(),
            cyclesNumber = attachment.cyclesQuantity.toBigInteger(),
            currentCycle = attachment.currentCycle.toBigInteger(),
            currentCycleDays = attachment.currentCycleDays.toBigInteger(),
            cyclesInterval = attachment.cyclesInterval.toBigInteger(),
            observation = attachment.observation?.unaccent()
        )

        val createGuiaRequest = CreateGuiaRequest(
            guiaNumber = totvsGuia.code,
            ansCode = AnsRegisterCode.ALICE.code,
            procedures = referenceGuiaProcedures.map { procedure ->
                GuiaProcedure(
                    code = procedure.procedureId!!,
                    description = healthcareResourcesByCode[procedure.procedureId!!]?.description ?: "NO DESCRIPTION",
                    quantity = procedure.extraGuiaInfo.quantity ?: 1,
                    status = procedure.status,
                    table = healthcareResourcesByCode[procedure.procedureId!!]?.tableType ?: "98"
                )
            },
            beneficiary = beneficiary,
            requester = toRequesterData(totvsGuia.cnpj!!),
            authorizationType = AnsAuthorizationType.PREVIOUS_AUTHORIZATION_REQUEST.code,
            attendanceCharacter = AttendanceCharacter.ELECTIVE.code,
            professional = toProfessional(totvsGuia.requestedByProfessional),
            requestDate = LocalDate.now(),
            guiaExternalCode = totvsGuia.externalCode,
            origin = origin,
            accidentIndication = AnsAccidentIndication.NOT_ACCIDENT.code,
            attendanceRegime = AnsAttendanceRegime.AMBULATORY.code
        )

        return Pair(chemotherapyRequestAttachment, createGuiaRequest).success()
    }

    private fun toProfessional(professionalIdentification: ProfessionalIdentification) =
        Professional(
            name = professionalIdentification.fullName?.unaccent().nullIfBlank() ?: DEFAULT_PROFESSIONAL_NAME,
            councilNumber = professionalIdentification.councilNumber.nullIfBlank() ?: DEFAULT_COUNCIL_NUMBER,
            council = professionalIdentification.council,
            councilState = professionalIdentification.councilState,
            phoneNumber = professionalIdentification.phone?.onlyNumbers(),
            email = professionalIdentification.email,
            cbo = professionalIdentification.cboCode ?: DEFAULT_CBO,
        )

    private fun bodySurface(height: BigDecimal, weight: BigDecimal): BigDecimal {
        val mc = MathContext.DECIMAL128

        val heightMultiplyWeight = height.multiply(weight, mc)
        val divisor = BigDecimal(3600)
        val divisionResult = heightMultiplyWeight.divide(divisor, mc).toDouble()

        return BigDecimal(sqrt(divisionResult), mc).setScale(2, RoundingMode.HALF_UP)
    }

    private fun Sex?.toSexTuss() = when (this) {
        Sex.MALE -> "1"
        Sex.FEMALE -> "3"
        Sex.INTERSEX -> throw InvalidSexFieldException()
        null -> throw RequiredFieldException("sex")
    }

    private fun List<br.com.alice.data.layer.models.RequestedDrugs>.toRequestedDrugs() =
        RequestedDrugs(
            requestedDrug = this.map {
                RequestedDrug(
                    probableDate = it.startDate,
                    identification = GuiaProcedure(
                        table = it.drugsIdentification.table,
                        code = it.drugsIdentification.code,
                        description = it.drugsIdentification.description.unaccent(),
                        dosage = it.totalCycleDosage,
                        status = it.status
                    ),
                    dosesQuantity = it.totalCycleDosage,
                    unitMeasure = it.unitOfMeasurement.code,
                    administrationRoute = it.administrationRoute.code,
                    frequency = it.frequency.toBigInteger()
                )
            }
        )

    private suspend fun toRequesterData(cnpj: String): RequesterData =
        providerUnitService.getByCnpj(cnpj).map { providerUnit ->
            RequesterData(
                providerCode = cnpj,
                name = providerUnit.name.unaccent()
            )
        }.get()

    private fun br.com.alice.data.layer.models.OncologicalDiagnosis.toChemotherapyOncologicalDiagnosis() =
        ChemotherapyOncologicalDiagnosis(
            chemoDiagnosis = OncologicalDiagnosis(
                diagnosisDate = this.diagnosisDate,
                diagnosisCID = listOf(this.healthCondition.code ?: DEFAULT_CID),
                local = this.stage.code,
                purpose = this.purpose.code,
                ecog = this.ecoGt.code,
                relevantInfo = this.relevantInformations?.sanitizeField(),
            ),
            tumor = this.tumor.code,
            nodule = this.nodule.code,
            metastasis = this.metastasis.code,
            chemotherapyType = this.type.code,
            therapeuticPlan = this.therapeuticPlan?.sanitizeField() ?: "NO DESCRIPTION"
        )

    private fun String?.sanitizeField() =
        this?.unaccent()?.uppercase()?.onlyDigits()?.trim()?.nullIfBlank() ?: "Not informed"
}
