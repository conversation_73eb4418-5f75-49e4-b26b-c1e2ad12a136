package br.com.alice.tiss.consumers

import br.com.alice.common.core.extensions.isNotNullOrBlank
import br.com.alice.common.logging.logger
import br.com.alice.data.layer.models.MvAuthorizedProcedureStatus
import br.com.alice.exec.indicator.client.MvAuthorizedProcedureService
import br.com.alice.exec.indicator.client.TotvsGuiaService
import br.com.alice.exec.indicator.events.GuiaCreatedEvent
import br.com.alice.tiss.consumers.converters.GuiaCreatedRequestBuilder
import br.com.alice.tiss.services.internal.GuiaService
import com.github.kittinunf.result.flatMap
import com.github.kittinunf.result.success
import com.sun.xml.ws.client.ClientTransportException
import jakarta.xml.ws.WebServiceException

class GuiaCreatedConsumer(
    private val guiaService: GuiaService,
    private val guiaCreatedRequestBuilder: GuiaCreatedRequestBuilder,
    private val mvAuthorizedProcedureService: MvAuthorizedProcedureService,
    private val totvsGuiaService: TotvsGuiaService
) : AutoRetryableConsumer(ClientTransportException::class, WebServiceException::class) {

    companion object {
        private const val ALICE_HOUSE_PINHEIROS_CNPJ = "35819524000193"
        private const val ALICE_HOUSE_MOEMA_CNPJ = "35819524000274"
        private const val GENERIC_RDA_CNPJ = "00000000000"
    }

    suspend fun handle(event: GuiaCreatedEvent) = withSubscribersEnvironment {
        logger.info(
            "Processing GuiaCreatedEvent on tiss-domain-service",
            "event" to event
        )

        val payload = event.payload

        val executorCnpj = if (payload.executorCnpj == GENERIC_RDA_CNPJ || payload.executorCnpj == ALICE_HOUSE_MOEMA_CNPJ) {
            ALICE_HOUSE_PINHEIROS_CNPJ
        } else payload.executorCnpj

        val isExecution = event.payload.executorCnpj.isNotNullOrBlank()
        val totvsGuiaId = event.payload.totvsGuia.id

        if (isExecution) {
            val existingProcedures = mvAuthorizedProcedureService.findByTotvsGuiaId(totvsGuiaId).get()

            val hasAlreadyCreatedAndExecutedGuide = existingProcedures.any { procedure ->
                procedure.status == MvAuthorizedProcedureStatus.EXECUTED &&
                        procedure.guiaExecutionCode.isNotNullOrBlank() &&
                        event.payload.procedures.map { it.id }.contains(procedure.id)
            }


            if (hasAlreadyCreatedAndExecutedGuide) {
                logger.info(
                    "GuiaCreatedEvent already processed",
                    "totvs_guia_id" to event.payload.totvsGuia.id,
                )

                return@withSubscribersEnvironment true.success()
            }
        } else {
            val totvsGuia = totvsGuiaService.get(totvsGuiaId).get()

            if (totvsGuia.externalCode != null) {
                logger.info(
                    "GuiaCreatedEvent already processed",
                    "totvs_guia_id" to event.payload.totvsGuia.id,
                )

                return@withSubscribersEnvironment true.success()
            }
        }

        guiaCreatedRequestBuilder.build(
            totvsGuia = payload.totvsGuia,
            procedures = payload.procedures,
            beneficiary = payload.beneficiary,
            executorCnpj = executorCnpj,
            origin = payload.origin,
            executorProviderUnitId = if (executorCnpj != null) payload.providerUnitId else null,
        ).flatMap { guiaService.createGuiaExam(it) }

    }
}
