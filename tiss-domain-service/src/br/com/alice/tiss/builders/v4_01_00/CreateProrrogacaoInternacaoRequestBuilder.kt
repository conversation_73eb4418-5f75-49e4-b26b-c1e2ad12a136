package br.com.alice.tiss.builders.v4_01_00

import br.com.alice.common.core.extensions.onlyAlphanumeric
import br.com.alice.data.layer.models.AnsProfessionalCouncil
import br.com.alice.data.layer.models.AnsUF
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.tiss.models.CreateGuiaHospitalizationExtensionRequest
import br.com.alice.tiss.models.TissHeaderParams
import br.com.alice.tiss.totvs.v4_01_00.commom.CtContratadoDados
import br.com.alice.tiss.totvs.v4_01_00.commom.CtContratadoProfissionalDados
import br.com.alice.tiss.totvs.v4_01_00.commom.CtProcedimentoDados
import br.com.alice.tiss.totvs.v4_01_00.commom.CtSolicitacaoProcedimento
import br.com.alice.tiss.totvs.v4_01_00.commom.CtmProrrogacaoSolicitacaoGuia
import br.com.alice.tiss.totvs.v4_01_00.commom.SolicitacaoProcedimentoWS
import br.com.alice.tiss.utils.TissTransactionTypeEnum
import java.time.LocalDate
import java.time.ZoneId
import java.util.Date
import java.util.GregorianCalendar
import javax.xml.datatype.DatatypeFactory
import javax.xml.datatype.XMLGregorianCalendar
import kotlin.math.min

object CreateProrrogacaoInternacaoRequestBuilder {

    const val hash = "11111111111111111111111111111111"

    fun buildSolicitacaoProrrogacaoWS(
        createGuiaHospitalizationExtensionRequest: CreateGuiaHospitalizationExtensionRequest,
        tissVersion: String
    ): SolicitacaoProcedimentoWS {
        val providerCnpj = createGuiaHospitalizationExtensionRequest.requester.providerCode
        val header = TissTransactionHeaderBuilder.buildTissHeader(
            TissHeaderParams(
                transactionType = TissTransactionTypeEnum.SOLICITACAO_PROCEDIMENTOS,
                providerCnpj = providerCnpj,
                tissVersion = tissVersion,
                origin = createGuiaHospitalizationExtensionRequest.origin
            )
        )

        val ctSolicitacaoProrrogacao = buildCtSolicitacaoInternacao(createGuiaHospitalizationExtensionRequest)
        val ctSolicitacaoProcedimento = CtSolicitacaoProcedimento()

        val solicitacaoProrrogacao = SolicitacaoProcedimentoWS()
        solicitacaoProrrogacao.hash = hash
        solicitacaoProrrogacao.cabecalho = header
        ctSolicitacaoProcedimento.solicitacaoProrrogacao = ctSolicitacaoProrrogacao
        solicitacaoProrrogacao.solicitacaoProcedimento = ctSolicitacaoProcedimento

        return solicitacaoProrrogacao
    }

    private fun buildCtSolicitacaoInternacao(
        request: CreateGuiaHospitalizationExtensionRequest,
    ): CtmProrrogacaoSolicitacaoGuia {
        val ctmProrrogacaoSolicitacaoGuia = CtmProrrogacaoSolicitacaoGuia()

        ctmProrrogacaoSolicitacaoGuia.registroANS = request.ansCode
        ctmProrrogacaoSolicitacaoGuia.numeroGuiaPrestador = request.guiaNumber
        ctmProrrogacaoSolicitacaoGuia.nrGuiaReferenciada = request.referenceGuiaNumber
        ctmProrrogacaoSolicitacaoGuia.nomeContratadoSolicitante = request.requester.name
        ctmProrrogacaoSolicitacaoGuia.dataSolicitacao = convertDate(request.requestDate)

        val dadosBeneficiario = CtmProrrogacaoSolicitacaoGuia.DadosBeneficiario()
        dadosBeneficiario.numeroCarteira = request.beneficiary.nationalId

        val ctContratadoDados = CtContratadoDados()
        ctContratadoDados.codigoPrestadorNaOperadora = request.requester.providerCode

        val ctContratadoProfissionalDados = CtContratadoProfissionalDados()
        ctContratadoProfissionalDados.nomeProfissional = request.professional.name
        ctContratadoProfissionalDados.numeroConselhoProfissional = request.professional.councilNumber
        ctContratadoProfissionalDados.cbos = request.professional.cbo

        val council = AnsProfessionalCouncil.valueOf(request.professional.council.toString()).code
        ctContratadoProfissionalDados.conselhoProfissional = council

        val uf = AnsUF.valueOf(request.professional.councilState.toString()).code
        ctContratadoProfissionalDados.uf = uf


        val dadosInternacao = CtmProrrogacaoSolicitacaoGuia.DadosInternacao()
        dadosInternacao.qtDiariasAdicionais = request.additionalDays.toBigInteger()
        dadosInternacao.indicacaoClinica = request.clinicalIndication.trim()

        val procedimentosSolicitados = buildProcedureList(request.procedures)

        ctmProrrogacaoSolicitacaoGuia.dadosBeneficiario = dadosBeneficiario
        ctmProrrogacaoSolicitacaoGuia.dadosContratadoSolicitante = ctContratadoDados
        ctmProrrogacaoSolicitacaoGuia.dadosProfissionalSolicitante = ctContratadoProfissionalDados
        ctmProrrogacaoSolicitacaoGuia.dadosInternacao = dadosInternacao
        ctmProrrogacaoSolicitacaoGuia.procedimentosAdicionais.addAll(procedimentosSolicitados)

        return ctmProrrogacaoSolicitacaoGuia
    }

    private fun convertDate(dateToParse: LocalDate): XMLGregorianCalendar? {
        val date = Date.from(
            dateToParse.atStartOfDay(ZoneId.systemDefault()).toInstant()
        )
        val gregorianCalendar = GregorianCalendar()
        gregorianCalendar.time = date
        val xmlGregorianCalendar = DatatypeFactory.newInstance().newXMLGregorianCalendar(gregorianCalendar)
        return xmlGregorianCalendar
    }

    private fun buildProcedureList(procedures: List<GuiaProcedure>) =
        procedures.sortedBy { it.code }.map { procedure ->
            val dadosProcedimento = CtProcedimentoDados()
            val description = procedure.description.onlyAlphanumeric()
            dadosProcedimento.codigoTabela = procedure.table
            dadosProcedimento.codigoProcedimento = procedure.code.trim()
            dadosProcedimento.descricaoProcedimento = description.substring(
                0, min(150, description.length)
            )

            val procedimentoSolicitado = CtmProrrogacaoSolicitacaoGuia.ProcedimentosAdicionais()
            procedimentoSolicitado.procedimento = dadosProcedimento
            procedimentoSolicitado.quantidadeSolicitada = procedure.quantity?.toBigInteger()

            procedimentoSolicitado
        }
}
