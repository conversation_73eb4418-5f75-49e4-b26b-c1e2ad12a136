
package br.com.alice.tiss.totvs.v4_01_00.commom;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlSeeAlso;
import jakarta.xml.bind.annotation.XmlType;


/**
 * <p>Classe Java de ct_contratadoDados complex type.
 * 
 * <p>O seguinte fragmento do esquema especifica o conteúdo esperado contido dentro desta classe.
 * 
 * <pre>
 * &lt;complexType name="ct_contratadoDados">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;choice>
 *         &lt;element name="codigoPrestadorNaOperadora" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_texto14"/>
 *         &lt;element name="cpfContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_CPF"/>
 *         &lt;element name="cnpjContratado" type="{http://www.ans.gov.br/padroes/tiss/schemas}st_CNPJ"/>
 *       &lt;/choice>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ct_contratadoDados", propOrder = {
    "codigoPrestadorNaOperadora",
    "cpfContratado",
    "cnpjContratado"
})
@XmlSeeAlso({
    CtmConsultaGuia.ContratadoExecutante.class
})
public class CtContratadoDados {

    protected String codigoPrestadorNaOperadora;
    protected String cpfContratado;
    protected String cnpjContratado;

    /**
     * Obtém o valor da propriedade codigoPrestadorNaOperadora.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCodigoPrestadorNaOperadora() {
        return codigoPrestadorNaOperadora;
    }

    /**
     * Define o valor da propriedade codigoPrestadorNaOperadora.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCodigoPrestadorNaOperadora(String value) {
        this.codigoPrestadorNaOperadora = value;
    }

    /**
     * Obtém o valor da propriedade cpfContratado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCpfContratado() {
        return cpfContratado;
    }

    /**
     * Define o valor da propriedade cpfContratado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCpfContratado(String value) {
        this.cpfContratado = value;
    }

    /**
     * Obtém o valor da propriedade cnpjContratado.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCnpjContratado() {
        return cnpjContratado;
    }

    /**
     * Define o valor da propriedade cnpjContratado.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCnpjContratado(String value) {
        this.cnpjContratado = value;
    }

}
