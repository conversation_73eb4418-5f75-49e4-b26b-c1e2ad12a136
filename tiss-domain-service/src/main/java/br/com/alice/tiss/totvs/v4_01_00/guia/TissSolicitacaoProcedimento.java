
package br.com.alice.tiss.totvs.v4_01_00.guia;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "tissSolicitacaoProcedimento", targetNamespace = "http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaoprocedimento/v40100", wsdlLocation = "https://alicetecnologia153310.protheus.cloudtotvs.com.br:4020/pp01/tissSolicitacaoProcedimento.apw?wsdl")
public class TissSolicitacaoProcedimento
    extends Service
{

    private final static URL TISSSOLICITACAOPROCEDIMENTO_WSDL_LOCATION;
    private final static WebServiceException TISSSOLICITACAOPROCEDIMENTO_EXCEPTION;
    private final static QName TISSSOLICITACAOPROCEDIMENTO_QNAME = new QName("http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaoprocedimento/v40100", "tissSolicitacaoProcedimento");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("file:/Users/<USER>/Documents/wsdl/tissSolicitacaoProcedimentoV4_01_00-prod.wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        TISSSOLICITACAOPROCEDIMENTO_WSDL_LOCATION = url;
        TISSSOLICITACAOPROCEDIMENTO_EXCEPTION = e;
    }

    public TissSolicitacaoProcedimento() {
        super(__getWsdlLocation(), TISSSOLICITACAOPROCEDIMENTO_QNAME);
    }

    public TissSolicitacaoProcedimento(WebServiceFeature... features) {
        super(__getWsdlLocation(), TISSSOLICITACAOPROCEDIMENTO_QNAME, features);
    }

    public TissSolicitacaoProcedimento(URL wsdlLocation) {
        super(wsdlLocation, TISSSOLICITACAOPROCEDIMENTO_QNAME);
    }

    public TissSolicitacaoProcedimento(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, TISSSOLICITACAOPROCEDIMENTO_QNAME, features);
    }

    public TissSolicitacaoProcedimento(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public TissSolicitacaoProcedimento(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns TissSolicitacaoProcedimentoPortType
     */
    @WebEndpoint(name = "tissSolicitacaoProcedimento_Port")
    public TissSolicitacaoProcedimentoPortType getTissSolicitacaoProcedimentoPort() {
        return super.getPort(new QName("http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaoprocedimento/v40100", "tissSolicitacaoProcedimento_Port"), TissSolicitacaoProcedimentoPortType.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link jakarta.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns TissSolicitacaoProcedimentoPortType
     */
    @WebEndpoint(name = "tissSolicitacaoProcedimento_Port")
    public TissSolicitacaoProcedimentoPortType getTissSolicitacaoProcedimentoPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaoprocedimento/v40100", "tissSolicitacaoProcedimento_Port"), TissSolicitacaoProcedimentoPortType.class, features);
    }

    private static URL __getWsdlLocation() {
        if (TISSSOLICITACAOPROCEDIMENTO_EXCEPTION!= null) {
            throw TISSSOLICITACAOPROCEDIMENTO_EXCEPTION;
        }
        return TISSSOLICITACAOPROCEDIMENTO_WSDL_LOCATION;
    }

}
