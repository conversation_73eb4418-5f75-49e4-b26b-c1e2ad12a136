
package br.com.alice.tiss.totvs.v4_01_00.guia;

import jakarta.xml.ws.*;

import javax.xml.namespace.QName;
import java.net.MalformedURLException;
import java.net.URL;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 */
@WebServiceClient(name = "tissLoteAnexo", targetNamespace = "http://www.ans.gov.br/tiss/ws/tipos/tissloteanexo/v40100", wsdlLocation = "https://alicetecnologia153310.protheus.cloudtotvs.com.br:4020/pp01/tissLoteAnexo.apw?wsdl")
public class TissLoteAnexo
        extends Service {

    private final static URL TISSLOTEANEXO_WSDL_LOCATION;
    private final static WebServiceException TISSLOTEANEXO_EXCEPTION;
    private final static QName TISSLOTEANEXO_QNAME = new QName("http://www.ans.gov.br/tiss/ws/tipos/tissloteanexo/v40100", "tissLoteAnexo");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("file:/Users/<USER>/dev/temp/v4_01_00_fix/tissLoteAnexoV4_01_00.wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        TISSLOTEANEXO_WSDL_LOCATION = url;
        TISSLOTEANEXO_EXCEPTION = e;
    }

    public TissLoteAnexo() {
        super(__getWsdlLocation(), TISSLOTEANEXO_QNAME);
    }

    public TissLoteAnexo(WebServiceFeature... features) {
        super(__getWsdlLocation(), TISSLOTEANEXO_QNAME, features);
    }

    public TissLoteAnexo(URL wsdlLocation) {
        super(wsdlLocation, TISSLOTEANEXO_QNAME);
    }

    public TissLoteAnexo(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, TISSLOTEANEXO_QNAME, features);
    }

    public TissLoteAnexo(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public TissLoteAnexo(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    private static URL __getWsdlLocation() {
        if (TISSLOTEANEXO_EXCEPTION != null) {
            throw TISSLOTEANEXO_EXCEPTION;
        }
        return TISSLOTEANEXO_WSDL_LOCATION;
    }

    /**
     * @return returns TissLoteAnexoPortType
     */
    @WebEndpoint(name = "tissLoteAnexo_Port")
    public TissLoteAnexoPortType getTissLoteAnexoPort() {
        return super.getPort(new QName("http://www.ans.gov.br/tiss/ws/tipos/tissloteanexo/v40100", "tissLoteAnexo_Port"), TissLoteAnexoPortType.class);
    }

    /**
     * @param features A list of {@link jakarta.xml.ws.WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return returns TissLoteAnexoPortType
     */
    @WebEndpoint(name = "tissLoteAnexo_Port")
    public TissLoteAnexoPortType getTissLoteAnexoPort(WebServiceFeature... features) {
        return super.getPort(new QName("http://www.ans.gov.br/tiss/ws/tipos/tissloteanexo/v40100", "tissLoteAnexo_Port"), TissLoteAnexoPortType.class, features);
    }

}
