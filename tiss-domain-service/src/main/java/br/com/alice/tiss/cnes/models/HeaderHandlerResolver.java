package br.com.alice.tiss.cnes.models;

import jakarta.xml.ws.handler.Handler;
import jakarta.xml.ws.handler.HandlerResolver;
import jakarta.xml.ws.handler.PortInfo;

import java.util.ArrayList;
import java.util.List;

public class HeaderHandlerResolver implements HandlerResolver {

    private String user;
    private String pwd;

    public HeaderHandlerResolver(String user, String pwd) {
        this.user = user;
        this.pwd = pwd;
    }

    @Override
    public List<Handler> getHandlerChain(PortInfo portInfo) {

        List<Handler> handlerChain = new ArrayList<Handler>();

        HeaderHandlerCnes hh = new HeaderHandlerCnes(this.user, this.pwd);

        handlerChain.add(hh);

        return handlerChain;
    }
}
