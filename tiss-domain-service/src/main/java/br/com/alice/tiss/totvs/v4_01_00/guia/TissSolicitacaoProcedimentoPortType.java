
package br.com.alice.tiss.totvs.v4_01_00.guia;

import br.com.alice.tiss.totvs.v4_01_00.commom.AutorizacaoProcedimentoWS;
import br.com.alice.tiss.totvs.v4_01_00.commom.ObjectFactory;
import br.com.alice.tiss.totvs.v4_01_00.commom.SolicitacaoProcedimentoWS;
import br.com.alice.tiss.totvs.v4_01_00.commom.TissFault;
import jakarta.jws.WebMethod;
import jakarta.jws.WebParam;
import jakarta.jws.WebResult;
import jakarta.jws.WebService;
import jakarta.jws.soap.SOAPBinding;
import jakarta.xml.bind.annotation.XmlSeeAlso;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.2.9-b130926.1035
 * Generated source version: 2.2
 * 
 */
@WebService(name = "tissSolicitacaoProcedimento_PortType", targetNamespace = "http://www.ans.gov.br/tiss/ws/tipos/tisssolicitacaoprocedimento/v40100")
@SOAPBinding(parameterStyle = SOAPBinding.ParameterStyle.BARE)
@XmlSeeAlso({
    ObjectFactory.class
})
public interface TissSolicitacaoProcedimentoPortType {


    /**
     * 
     * @param solicitacaoProcedimento
     * @return
     *     returns guia.create.AutorizacaoProcedimentoWS
     * @throws TissFault
     */
    @WebMethod(operationName = "tissSolicitacaoProcedimento_Operation")
    @WebResult(name = "autorizacaoProcedimentoWS", targetNamespace = "http://www.ans.gov.br/padroes/tiss/schemas", partName = "autorizacaoProcedimento")
    public AutorizacaoProcedimentoWS tissSolicitacaoProcedimentoOperation(
        @WebParam(name = "solicitacaoProcedimentoWS", targetNamespace = "http://www.ans.gov.br/padroes/tiss/schemas", partName = "solicitacaoProcedimento")
        SolicitacaoProcedimentoWS solicitacaoProcedimento)
        throws TissFault
    ;

}
