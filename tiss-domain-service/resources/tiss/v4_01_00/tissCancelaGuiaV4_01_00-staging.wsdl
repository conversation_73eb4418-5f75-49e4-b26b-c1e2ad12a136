<?xml version="1.0" encoding="ISO-8859-1"?>
<definitions xmlns:tns="http://www.ans.gov.br/tiss/ws/tipos/tisscancelaguia/v40100" xmlns:ans="http://www.ans.gov.br/padroes/tiss/schemas" xmlns="http://schemas.xmlsoap.org/wsdl/"
             xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" name="tissCancelaGuia" targetNamespace="http://www.ans.gov.br/tiss/ws/tipos/tisscancelaguia/v40100">
	<types>
        <schema xmlns="http://www.w3.org/2001/XMLSchema">
			<import namespace="http://www.ans.gov.br/padroes/tiss/schemas" schemaLocation="tissWebServicesV4_01_00.xsd"/>
		</schema>
	</types>
	<message name="tissCancelaGuia_Request">
		<part name="cancelaGuia" element="ans:cancelaGuiaWS"/>
	</message>
	<message name="tissCancelaGuia_Response">
		<part name="reciboCancelaGuia" element="ans:reciboCancelaGuiaWS"/>
	</message>
	<message name="tissFault">
		<part name="tissFault" element="ans:tissFaultWS"/>
	</message>
	<portType name="tissCancelaGuia_PortType">
		<operation name="tissCancelaGuia_Operation">
			<input message="tns:tissCancelaGuia_Request"/>
			<output message="tns:tissCancelaGuia_Response"/>
			<fault name="TissFault" message="tns:tissFault"/>
		</operation>
	</portType>
	<binding name="tissCancelaGuia_Binding" type="tns:tissCancelaGuia_PortType">
		<soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
		<operation name="tissCancelaGuia_Operation">
			<soap:operation soapAction=""/>
			<input>
				<soap:body use="literal"/>
			</input>
			<output>
				<soap:body use="literal"/>
			</output>
			<fault name="TissFault">
				<soap:fault name="TissFault" use="literal"/>
			</fault>
		</operation>
	</binding>
	<service name="tissCancelaGuia">
		<port name="tissCancelaGuia_Port" binding="tns:tissCancelaGuia_Binding">
			<soap:address location="https://alicetecnologia153313.protheus.cloudtotvs.com.br:1232/pp01/tissCancelaGuiaV4_01_00.apw"/>
		</port>
	</service>
</definitions>
