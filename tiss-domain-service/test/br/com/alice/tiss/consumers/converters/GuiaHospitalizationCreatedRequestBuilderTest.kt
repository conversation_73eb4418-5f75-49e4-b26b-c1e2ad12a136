package br.com.alice.tiss.consumers.converters

import br.com.alice.common.MvUtil
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.common.models.CouncilType
import br.com.alice.common.models.State
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AnsAccidentIndication
import br.com.alice.data.layer.models.AnsAttendanceRegime
import br.com.alice.data.layer.models.AnsRegisterCode
import br.com.alice.data.layer.models.ExtraGuiaInfo
import br.com.alice.data.layer.models.ProfessionalIdentification
import br.com.alice.data.layer.models.ProfessionalSpecialty
import br.com.alice.exec.indicator.client.HealthcareResourceService
import br.com.alice.exec.indicator.client.HospitalizationInfoService
import br.com.alice.exec.indicator.models.Beneficiary
import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.exec.indicator.models.GuiaProcedure
import br.com.alice.exec.indicator.models.Professional
import br.com.alice.exec.indicator.models.RequesterData
import br.com.alice.provider.client.ProviderUnitService
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.AfterTest
import kotlin.test.Test

class GuiaHospitalizationCreatedRequestBuilderTest {

    private val healthcareResourceService: HealthcareResourceService = mockk()
    private val hospitalizationInfoService: HospitalizationInfoService = mockk()
    private val providerUnitService: ProviderUnitService = mockk()

    private val builder = GuiaHospitalizationCreatedRequestBuilder(healthcareResourceService,
        hospitalizationInfoService, providerUnitService)

    private val procedure = TestModelFactory.buildMvAuthorizedProcedure(
        extraGuiaInfo = ExtraGuiaInfo(procedureType = MvUtil.TISS.HOSPITALIZATION, newBorn = true, quantity = 3),
        requestedByProfessional = ProfessionalIdentification(
            email = "<EMAIL>",
            phone = "(85) 98765-4321",
            councilNumber = "123456",
            councilState = State.CE,
            council = CouncilType.CRM,
            fullName = "João Pereira",
            cboCode = "225120",
            specialty = ProfessionalSpecialty(
                id = 1L,
                name = "Cardiologia"
            )
        )
    )
    private val procedures = listOf(procedure)
    private val totvsGuia = TestModelFactory.buildTotvsGuia()
    private val beneficiary = Beneficiary(name = "José da Silva", nationalId = "19218341456", newBornAttendance = false)

    private val healthcareResource = TestModelFactory.buildHealthcareResource()
    private val providerUnit = TestModelFactory.buildProviderUnit(name = "Hospital Alemão Oswaldo Cruz")

    @AfterTest
    fun clean() = clearAllMocks()

    @Test
    fun `#build builds the GuiaHospitalizationCreatedRequest`() = runBlocking {
        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo(clinicalIndication = "Paciente veio de sua residência acompanhado.\n" +
                " Solicito exames.\n" +
                " Urgente! ",
            providerUnitId = providerUnit.id, opmeIndication = true)

        coEvery {
            healthcareResourceService.findByCodes(listOf(procedure.procedureId!!))
        } returns listOf(healthcareResource).success()

        coEvery {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
        } returns hospitalizationInfo.success()

        coEvery {
            providerUnitService.get(hospitalizationInfo.providerUnitId)
        } returns providerUnit.success()

        val result = builder.build(procedures, totvsGuia, beneficiary, GuiaOrigin.EITA)
        val request = result.get()

        assertThat(request.guiaNumber).isEqualTo(totvsGuia.code)
        // use newBornAttendance from MvAuthorizedProcedure, not from Beneficiary
        assertThat(request.beneficiary).isEqualTo(Beneficiary(name = "Jose da Silva", nationalId = "19218341456", newBornAttendance = true))

        assertThat(request.procedures).isEqualTo(listOf(
            GuiaProcedure(code = procedure.procedureId!!, description = "NO DESCRIPTION", quantity = 3, status = procedure.status, table = "98")
        ))
        assertThat(request.requester).isEqualTo(RequesterData(name = "Hospital Alemao Oswaldo Cruz", providerCode = providerUnit.cnpj!!))
        assertThat(request.professional).isEqualTo(
            Professional(name = "Joao Pereira", cbo = "225120", councilNumber = "123456", council = CouncilType.CRM,
                councilState = State.CE, phoneNumber = "85987654321", email = "<EMAIL>")
        )
        assertThat(request.guiaExternalCode).isEqualTo(totvsGuia.externalCode)
        assertThat(request.origin).isEqualTo(GuiaOrigin.EITA)
        assertThat(request.attendanceRegime).isEqualTo(AnsAttendanceRegime.HOSPITALIZATION.code)
        assertThat(request.accidentIndication).isEqualTo(AnsAccidentIndication.NOT_ACCIDENT.code)
        assertThat(request.admissionType).isEqualTo(hospitalizationInfo.type.code)
        assertThat(request.admissionDaysRequested).isEqualTo(hospitalizationInfo.numberOfDays.toBigInteger())
        assertThat(request.opmeIndicator).isEqualTo(hospitalizationInfo.opmeIndication)
        assertThat(request.quimioterapyIndicator).isEqualTo(hospitalizationInfo.chemotherapyIndication)
        assertThat(request.clinicalIndication).isEqualTo("PACIENTE VEIO DE SUA RESIDENCIA ACOMPANHADO SOLICITO EXAMES URGENTE")
        assertThat(request.diagnosisCID).isEqualTo(hospitalizationInfo.healthCondition.code!!)
        assertThat(request.clinicalAttachment).isNull()
        assertThat(request.recommendedAdmissionDate).isEqualTo(hospitalizationInfo.suggestedDate)
        assertThat(request.ansCode).isEqualTo(AnsRegisterCode.ALICE.code)
        assertThat(request.authorizationType).isEqualTo("1")
        assertThat(request.attendanceCharacter).isEqualTo(hospitalizationInfo.attendanceCharacter.name)

        coVerifyOnce { healthcareResourceService.findByCodes(any()) }
        coVerifyOnce { hospitalizationInfoService.getByTotvsGuiaId(any()) }
        coVerifyOnce { providerUnitService.get(any()) }
    }

    @Test
    fun `#build builds the GuiaHospitalizationCreatedRequest with default clinicalIndication when it is null`() = runBlocking {
        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo(clinicalIndication = null,
            providerUnitId = providerUnit.id, opmeIndication = true)

        coEvery {
            healthcareResourceService.findByCodes(listOf(procedure.procedureId!!))
        } returns listOf(healthcareResource).success()

        coEvery {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
        } returns hospitalizationInfo.success()

        coEvery {
            providerUnitService.get(hospitalizationInfo.providerUnitId)
        } returns providerUnit.success()

        val result = builder.build(procedures, totvsGuia, beneficiary, GuiaOrigin.EITA)
        val request = result.get()

        assertThat(request.clinicalIndication).isEqualTo("Not informed")

        coVerifyOnce { healthcareResourceService.findByCodes(any()) }
        coVerifyOnce { hospitalizationInfoService.getByTotvsGuiaId(any()) }
        coVerifyOnce { providerUnitService.get(any()) }
    }

    @Test
    fun `#build builds the GuiaHospitalizationCreatedRequest with default clinicalIndication when it is blank`() = runBlocking {
        val hospitalizationInfo = TestModelFactory.buildHospitalizationInfo(clinicalIndication = "   ",
            providerUnitId = providerUnit.id, opmeIndication = true)

        coEvery {
            healthcareResourceService.findByCodes(listOf(procedure.procedureId!!))
        } returns listOf(healthcareResource).success()

        coEvery {
            hospitalizationInfoService.getByTotvsGuiaId(totvsGuia.id)
        } returns hospitalizationInfo.success()

        coEvery {
            providerUnitService.get(hospitalizationInfo.providerUnitId)
        } returns providerUnit.success()

        val result = builder.build(procedures, totvsGuia, beneficiary, GuiaOrigin.EITA)
        val request = result.get()

        assertThat(request.clinicalIndication).isEqualTo("Not informed")

        coVerifyOnce { healthcareResourceService.findByCodes(any()) }
        coVerifyOnce { hospitalizationInfoService.getByTotvsGuiaId(any()) }
        coVerifyOnce { providerUnitService.get(any()) }
    }

}
