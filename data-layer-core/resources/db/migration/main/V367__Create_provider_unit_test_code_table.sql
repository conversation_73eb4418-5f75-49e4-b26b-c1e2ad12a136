CREATE TABLE IF NOT EXISTS provider_unit_test_code (
    id UUID NOT NULL PRIMARY KEY,
    version INTEGER NOT NULL,
    created_at timestamp NOT NULL,
    updated_at timestamp NOT NULL,
    test_code_id UUID NOT NULL,
    provider_unit_id UUID NOT NULL,
    title TEXT,
    FOREI<PERSON><PERSON> KEY (test_code_id) REFERENCES test_code(id),
    FOREIGN KEY (provider_unit_id) REFERENCES provider_unit(id)
)
