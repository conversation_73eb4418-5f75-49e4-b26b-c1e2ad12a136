CREATE TABLE IF NOT EXISTS member_invoice_group (
    id UUID PRIMARY KEY,
    external_id TEXT NOT NULL,
    member_invoice_ids JSONB NOT NULL DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    billing_accountable_party_id UUID,
    reference_date TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE NOT NULL,
    due_date TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE NOT NULL,
    status TEXT NOT NULL,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL
);

CREATE UNIQUE INDEX ON member_invoice_group(member_invoice_ids);
CREATE UNIQUE INDEX ON member_invoice_group(external_id);
