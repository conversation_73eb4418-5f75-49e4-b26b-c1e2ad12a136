CREATE TABLE IF NOT EXISTS sales_firm_staff (
   id UUID NOT NULL PRIMARY KEY,
   sales_firm_id UUID NOT NULL,
   first_name TEXT NOT NULL,
   last_name TEXT NOT NULL,
   email TEXT NOT NULL,
   role TEXT NOT NULL,
   status TEXT NOT NULL DEFAULT 'ACTIVE',
   version INTEGER DEFAULT 0 NOT NULL,
   created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
   updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
   FOREIGN KEY (sales_firm_id) REFERENCES sales_firm(id)
);

CREATE UNIQUE INDEX IF NOT EXISTS sales_firm_staff_email_unique_idx ON sales_firm_staff(email);
