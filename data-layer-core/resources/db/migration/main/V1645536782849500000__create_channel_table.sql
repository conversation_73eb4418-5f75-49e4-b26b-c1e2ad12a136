CREATE TABLE IF NOT EXISTS channel(
    id UUID NOT NULL PRIMARY KEY,
    channel_id TEXT UNIQUE NOT NULL,
    person_id UUID NOT NULL,
    name TEXT,
    type TEXT NOT NULL,
    status TEXT NOT NULL,
    owner_staff_id UUID,
    staff JSONB DEFAULT '{}'::<PERSON><PERSON><PERSON><PERSON>,
    staff_ids J<PERSON>N<PERSON> DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    tags JSONB DEFAULT '[]'::J<PERSON><PERSON><PERSON>,
    time_last_message TIMESTAMP WITHOUT TIME ZONE,
    is_archived BOOLEA<PERSON>,
    archived_at TIMESTAMP WITHOUT TIME ZONE,
    last_sync TIMESTAMP WITHOUT TIME ZONE,
    origin TEXT,
    merged_with TEX<PERSON>,
    app_version TEXT,
    became_async_at TIMESTAMP WITHOUT TIME ZONE,
    channel_person_type TEXT,
    in_progress BOOLEAN,
    waiting_since TIMES<PERSON><PERSON> WITHOUT TIME ZONE,
    follow_up <PERSON><PERSON><PERSON><PERSON>,
    channel_created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    channel_updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    version INTEGER NOT NULL,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL
);

CREATE INDEX IF NOT EXISTS channel_channel_id_idx ON channel(channel_id);
CREATE INDEX IF NOT EXISTS channel_person_id_idx ON channel(person_id);
CREATE INDEX IF NOT EXISTS channel_type_idx ON channel(type);
CREATE INDEX IF NOT EXISTS channel_status_idx ON channel(status);
CREATE INDEX IF NOT EXISTS channel_is_archived_idx ON channel(is_archived);
CREATE INDEX IF NOT EXISTS channel_in_progress_idx ON channel(in_progress);
CREATE INDEX IF NOT EXISTS channel_waiting_since_idx ON channel(waiting_since);
CREATE INDEX IF NOT EXISTS channel_channel_created_at_idx ON channel(channel_created_at);
CREATE INDEX IF NOT EXISTS channel_channel_updated_at_idx ON channel(channel_updated_at);
CREATE INDEX IF NOT EXISTS channel_last_sync_idx ON channel(last_sync);
CREATE INDEX IF NOT EXISTS channel_staff_idx ON channel USING GIN(staff);
CREATE INDEX IF NOT EXISTS channel_staff_ids_idx ON channel USING GIN(staff_ids);
