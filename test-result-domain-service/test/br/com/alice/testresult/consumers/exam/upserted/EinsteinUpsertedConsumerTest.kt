package br.com.alice.testresult.consumers.exam.upserted

import br.com.alice.common.core.PersonId
import br.com.alice.common.data.dsl.matchers.ResultAssert.Companion.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Exame
import br.com.alice.data.layer.models.Resultado
import br.com.alice.einsteinintegrationclient.events.EinsteinTestResultCreatedEvent
import br.com.alice.testresult.client.AliceTestResultBundleService
import br.com.alice.testresult.consumers.ConsumerTest
import br.com.alice.testresult.helpers.TestModelFactory.buildAliceTestResultBundle
import com.github.kittinunf.result.success
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import kotlin.test.Test

class EinsteinUpsertedConsumerTest : ConsumerTest() {
    private val aliceTestResultService: AliceTestResultBundleService = mockk()

    private val consumer = EinsteinUpsertedConsumer(
        aliceTestResultService
    )
    private val exame = Exame(
        idExame = "idExame",
        nomeExame = "nomeExame",
        codExame = "codExame",
        codLab = "codLab",
        TipoExame = "TipoExame",
        dtExecucao = "23/12/2021",
        disponivel = "disponivel",
        seguranca = "seguranca",
        medicoRespNome = "medicoRespNome",
        medicoRespCrm = "medicoRespCrm",
        laudo = "true",
        numAcesso = "numAcesso",
        resultado = Resultado(cdata = "Não Preenchido!"),
        posicao = "posicao",
        statusResultado = "statusResultado",
        qtdLinhas = "qtdLinhas",
        idProntuario = "idProntuario",
        prontuario = "prontuario",
        categoriaItem = "categoriaItem",
        dtExecucao2 = "dtExecucao2"
    )
    private val testResults =
        TestModelFactory.buildEinsteinResultadoExame(personId = PersonId(), exames = listOf(exame))


    @Test
    fun `#handleTestResultUpserted should process consumer`() = runBlocking {
        val event = EinsteinTestResultCreatedEvent(listOf(testResults))
        coEvery { aliceTestResultService.upsertByExternalId(any()) } returns buildAliceTestResultBundle().success()

        val response = consumer.handleTestResultUpserted(event)
        assertThat(response).isSuccess()

        coVerify { aliceTestResultService.upsertByExternalId(any()) }
    }
}
