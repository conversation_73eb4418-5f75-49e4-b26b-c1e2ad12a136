package br.com.alice.testresult.consumers.exam.file.db

import br.com.alice.common.RangeUUID
import br.com.alice.common.core.PersonId
import br.com.alice.common.core.exceptions.AuthorizationException
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.featureflag.withFeatureFlag
import br.com.alice.common.kafka.interfaces.KafkaProducerService
import br.com.alice.common.kafka.interfaces.ProducerResult
import br.com.alice.data.layer.models.AliceResultItem
import br.com.alice.data.layer.models.AliceTestResult
import br.com.alice.data.layer.models.FeatureNamespace
import br.com.alice.data.layer.models.ProviderIntegration
import br.com.alice.data.layer.models.ReferenceRange
import br.com.alice.dbintegrationclient.client.DbFileService
import br.com.alice.testresult.consumers.ConsumerTest
import br.com.alice.testresult.events.AliceTestResultUpsertedEvent
import br.com.alice.testresult.events.AliceTestResultWithUrlFileEvent
import br.com.alice.testresult.helpers.TestModelFactory
import br.com.alice.testresult.services.AliceTestResultFileService
import br.com.alice.testresult.services.ExamFileService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.Called
import io.mockk.called
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertFailsWith

class AliceTestResultDbConsumerTest : ConsumerTest() {
    private val dbFileService: DbFileService = mockk()
    private val kafkaProducer: KafkaProducerService = mockk()
    private val examFileService: ExamFileService = mockk()
    private val aliceTestResultFileService: AliceTestResultFileService = mockk()
    private val consumer =
        AliceTestResultDbConsumer(dbFileService, kafkaProducer, examFileService, aliceTestResultFileService)
    private val personId = PersonId()
    private val date = LocalDateTime.now()

    private val aliceItem =
        AliceResultItem(name = "name", result = "result", referenceRange = ReferenceRange.NOT_APPLIED)

    private val aliceTestResult = AliceTestResult(id = RangeUUID.generate(),
        name = "name",
        collectedAt = date,
        releasedAt = date.plusDays(1),
        externalReference = "COVID",
        items = listOf(aliceItem))

    private val urlDb = "exameDb.com.br/pdf"

    @Test
    fun `handlerDbAliceResultGetUrlConsumer should process consumer`() = runBlocking {
        val event = AliceTestResultUpsertedEvent(exam = aliceTestResult,
            provider = ProviderIntegration.DB,
            externalId = "40028922",
            personId = personId
        )
        val payload = event.payload
        coEvery {
            dbFileService.getUrl(payload.externalId, listOf(payload.exam.externalReference!!))
        } returns urlDb.success()
        coEvery { aliceTestResultFileService.findByReference(any()) } returns NotFoundException().failure()
        coEvery { kafkaProducer.produce(any(), any()) } returns ProducerResult(LocalDateTime.now(), "topic", 100)

        withFeatureFlag(FeatureNamespace.INTEROP, "should_db_file_download", true) {
            consumer.handlerDbAliceResultGetUrlConsumer(event)
        }

        coVerify(exactly = 1) { dbFileService.getUrl(any(), any()) }
        coVerify(exactly = 1) {
            kafkaProducer.produce(
                match<AliceTestResultWithUrlFileEvent> {
                    it.payload.urlFile == urlDb
                },
                match { it == payload.exam.externalReference })
        }
        coVerify(exactly = 1) { aliceTestResultFileService.findByReference(match { it == payload.exam.id }) }
    }

    @Test
    fun `handlerDbAliceResultGetUrlConsumer should not process with feature flag disabled`() = runBlocking {
        val event = AliceTestResultUpsertedEvent(exam = aliceTestResult,
            provider = ProviderIntegration.DB,
            externalId = "40028922",
            personId = personId
        )

        consumer.handlerDbAliceResultGetUrlConsumer(event)

        coVerify { dbFileService.getUrl(any(), any()) wasNot Called }
        coVerify {
            kafkaProducer.produce(any(), any()) wasNot Called
        }
        coVerify { aliceTestResultFileService.findByReference(any()) wasNot called }
    }

    @Test
    fun `#handlerDbAliceResultGetUrlConsumer should stop process when is failure at get alice file`(): Unit = runBlocking {
        assertFailsWith<AuthorizationException> {
            coEvery { aliceTestResultFileService.findByReference(any()) } returns AuthorizationException("").failure()

            val event = AliceTestResultUpsertedEvent(
                exam = aliceTestResult,
                provider = ProviderIntegration.DB,
                externalId = "40028922",
                personId = personId
            )

            withFeatureFlag(FeatureNamespace.INTEROP, "should_db_file_download", true) {
                consumer.handlerDbAliceResultGetUrlConsumer(event)
            }

            coVerify(exactly = 1) { aliceTestResultFileService.findByReference(match { it == event.payload.exam.id }) }
            coVerify { dbFileService.getUrl(any(), any()) wasNot Called }
            coVerify { kafkaProducer.produce(any()) wasNot Called }
        }
    }

    @Test
    fun `handlerDbAliceResultGetFileConsumer should process consumer`() = runBlocking {
        val event = AliceTestResultWithUrlFileEvent(exam = aliceTestResult,
            provider = ProviderIntegration.DB,
            externalId = "40028922",
            personId = personId,
            urlFile = urlDb
        )

        val payload = event.payload
        val byteArray = ByteArray(1)
        coEvery {
            dbFileService.getFile(urlDb)
        } returns byteArray.success()
        coEvery { aliceTestResultFileService.findByReference(any()) } returns NotFoundException().failure()
        coEvery { examFileService.upsert(any()) } returns mockk()

        withFeatureFlag(FeatureNamespace.INTEROP, "should_db_file_download", true) {
            consumer.handlerDbAliceResultGetFileConsumer(event)
        }

        coVerify(exactly = 1) { dbFileService.getFile(any()) }
        coVerify(exactly = 1) { examFileService.upsert(any()) }
        coVerify(exactly = 1) { aliceTestResultFileService.findByReference(match { it == payload.exam.id }) }
    }

    @Test
    fun `handlerDbAliceResultGetFileConsumer should not process with feature flag disabled`() = runBlocking {
        val event = AliceTestResultWithUrlFileEvent(exam = aliceTestResult,
            provider = ProviderIntegration.DB,
            externalId = "40028922",
            personId = personId,
            urlFile = urlDb
        )

        consumer.handlerDbAliceResultGetFileConsumer(event)

        coVerify { dbFileService.getFile(any()) wasNot called }
        coVerify { examFileService.upsert(any()) wasNot called }
        coVerify { aliceTestResultFileService.findByReference(any()) wasNot called }
    }

    @Test
    fun `#handlerDbAliceResultGetFileConsumer should stop process when is failure at get alice file`(): Unit = runBlocking {
        assertFailsWith<AuthorizationException> {
            coEvery { aliceTestResultFileService.findByReference(any()) } returns AuthorizationException("").failure()

            val testResult = TestModelFactory.buildAliceTestResult(externalReference = "COVID")

            val event = AliceTestResultWithUrlFileEvent(
                exam = testResult,
                provider = ProviderIntegration.DB,
                externalId = "X",
                personId = PersonId(),
                urlFile = "google.com",
            )

            withFeatureFlag(FeatureNamespace.INTEROP, "should_db_file_download", true) {
                consumer.handlerDbAliceResultGetFileConsumer(event)
            }

            coVerify(exactly = 1) { aliceTestResultFileService.findByReference(match { it == event.payload.exam.id }) }
            coVerify { dbFileService.getFile(any()) wasNot called }
            coVerify { examFileService.upsert(any()) wasNot called }
        }
    }
}
