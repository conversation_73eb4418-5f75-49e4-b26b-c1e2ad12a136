package br.com.alice.api.wanda.model.healthcaremap

import br.com.alice.data.layer.models.Risk
import br.com.alice.data.layer.models.RiskDescription
import br.com.alice.data.layer.models.Staff
import java.time.LocalDateTime
import java.util.UUID

data class RiskCurrentResponse(
    val id: UUID,
    val addedBy: AddedBy,
    val riskDescription: RiskDescription,
    val addedAt: LocalDateTime
)

data class AddedBy(
    val staff: Staff? = null,
    val type: Risk.AddedByType
)
