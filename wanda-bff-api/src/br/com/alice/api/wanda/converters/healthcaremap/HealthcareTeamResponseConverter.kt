package br.com.alice.api.wanda.converters.healthcaremap

import br.com.alice.api.wanda.model.healthcaremap.HealthcareTeamSimpleResponse
import br.com.alice.api.wanda.model.healthcaremap.StaffSimpleResponse
import br.com.alice.common.Converter
import br.com.alice.common.map
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.Staff
import java.util.UUID

object HealthcareTeamResponseConverter : Converter<HealthcareTeam, HealthcareTeamSimpleResponse>(
    HealthcareTeam::class, HealthcareTeamSimpleResponse::class
) {
    fun convert(
        source: HealthcareTeam,
        staff: Map<UUID, Staff>
    ): HealthcareTeamSimpleResponse =
        HealthcareTeamResponseConverter.convert(
            source,
            map(HealthcareTeamSimpleResponse::name) from "Time ${staff.getValue(source.physicianStaffId).firstName}",
            map(HealthcareTeamSimpleResponse::staff) from source.staffIds.map {
                SimpleStaffConverter.convert(staff.getValue(it)) }
        )
}

object SimpleStaffConverter : Converter<Staff, StaffSimpleResponse>(
    Staff::class, StaffSimpleResponse::class
) {
    fun convert(
        source: Staff
    ): StaffSimpleResponse =
        SimpleStaffConverter.convert(
            source,
            map(StaffSimpleResponse::role) from (source.role),
            map(StaffSimpleResponse::roleDescription) from (source.role?.description)
        )
}
