package br.com.alice.api.wanda.controllers

import br.com.alice.api.wanda.model.Comment
import br.com.alice.api.wanda.model.WandaCommentRequest
import br.com.alice.api.wanda.model.WandaCommentResponse
import br.com.alice.common.Response
import br.com.alice.common.core.extensions.toPersonId
import br.com.alice.common.core.extensions.toUUID
import br.com.alice.common.extensions.flatMapPair
import br.com.alice.common.foldResponse
import br.com.alice.staff.client.StaffService
import br.com.alice.wanda.client.WandaCommentService
import br.com.alice.wanda.model.WandaCommentTransport
import com.github.kittinunf.result.map
import com.github.kittinunf.result.success
import java.util.UUID

class CommentController(
    private val wandaCommentService: WandaCommentService,
    private val staffService: StaffService
) : StaffController(staffService) {

    suspend fun create(personId: String, id: UUID, request: WandaCommentRequest): Response =
        wandaCommentService.create(
            WandaCommentTransport(
                personId = personId.toPersonId(),
                personHealthEventId = id,
                staffId = currentStaffId(),
                comment = request.comment
            )
        ).map { comment ->
            val staff = staffService.get(comment.staffId).get()
            Comment(
                id = comment.id,
                comment = comment.comment,
                addedAt = comment.addedAt.toString(),
                staff = staff
            ).let {
                WandaCommentResponse(
                    personHealthEventId = id.toString(),
                    comments = listOf(it)
                )
            }
        }.foldResponse()

    suspend fun getByTaskId(id: String): Response =
        wandaCommentService.getByTaskId(id.toUUID()).flatMapPair { comments ->
            val staffIds = comments.map { it.staffId }
            staffService.findByList(staffIds).get().associateBy { it.id }.success()
        }.map { (staffMap, comments) ->
            comments.map {
                Comment(
                    id = it.id,
                    comment = it.comment,
                    addedAt = it.addedAt.toString(),
                    staff = staffMap[it.staffId]!!
                )
            }
        }.map {
            WandaCommentResponse(
                personHealthEventId = id,
                comments = it
            )
        }.foldResponse()
}
