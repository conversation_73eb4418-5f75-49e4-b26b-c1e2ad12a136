package br.com.alice.api.wanda.controllers

import br.com.alice.api.wanda.ControllerTestHelper
import br.com.alice.api.wanda.model.healthcaremap.HealthcareTeamSimpleResponse
import br.com.alice.api.wanda.model.healthcaremap.StaffSimpleResponse
import br.com.alice.clinicalaccount.client.PersonClinicalAccountService
import br.com.alice.common.core.Role
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareAdditionalTeam
import br.com.alice.data.layer.models.HealthcareAdditionalTeamType
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.Staff
import br.com.alice.marauders.map.models.HealthcareMapType
import br.com.alice.staff.client.HealthcareAdditionalTeamService
import br.com.alice.staff.client.HealthcareTeamFilters
import br.com.alice.staff.client.HealthcareTeamService
import br.com.alice.staff.client.StaffService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.called
import io.mockk.clearMocks
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class HealthcareTeamControllerTest : ControllerTestHelper() {

    private val healthcareTeamService: HealthcareTeamService = mockk()
    private val healthcareAdditionalTeamService: HealthcareAdditionalTeamService = mockk()
    private val personClinicalAccountService: PersonClinicalAccountService = mockk()
    private val staffService: StaffService = mockk()
    private val physician = TestModelFactory.buildHealthcareTeamPhysician().copy(role = Role.MANAGER_PHYSICIAN)
    private val nurse = TestModelFactory.buildHealthcareTeamNurse().copy(role = Role.HEALTHCARE_TEAM_NURSE)
    private val digitalCareNurse = TestModelFactory.buildDigitalCareNurse().copy(role = Role.DIGITAL_CARE_NURSE)
    private val digitalCareNurse2 = TestModelFactory.buildDigitalCareNurse().copy(role = Role.DIGITAL_CARE_NURSE)
    private val healthCareTeam = TestModelFactory.buildHealthcareTeam(
        physicianStaffId = physician.id,
        nurseStaffId = nurse.id,
        digitalCareNurseStaffIds = listOf(digitalCareNurse.id, digitalCareNurse2.id)
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearMocks(healthcareTeamService, staffService)
        module.single { HealthcareTeamController(healthcareTeamService, healthcareAdditionalTeamService, staffService, personClinicalAccountService) }
    }

    @Test
    fun `#getTeamsForCareCoordination should return all teams`() {
        val expected = listOf(
            HealthcareTeamResponse(
                id = healthCareTeam.id.toString(),
                physicianStaffId = physician.id.toString(),
                nurseStaffId = nurse.id.toString(),
                digitalCareNurse1StaffId = digitalCareNurse.id.toString(),
                digitalCareNurse2StaffId = digitalCareNurse2.id.toString(),
                description = "${physician.firstName} ${physician.lastName} / ${nurse.firstName} ${nurse.lastName}"
            )
        )
        coEvery { healthcareTeamService.findBy(HealthcareTeamFilters()) } returns listOf(healthCareTeam).success()
        coEvery {
            staffService.findByList(listOf(physician.id, nurse.id, digitalCareNurse.id, digitalCareNurse2.id))
        } returns listOf(physician, nurse, digitalCareNurse).success()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<HealthcareTeamResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getTeamsForCareCoordination should return teams without digital care nurse`() {
        val team = healthCareTeam.copy(digitalCareNurseStaffIds = emptyList())
        val expected = listOf(
            HealthcareTeamResponse(
                id = team.id.toString(),
                physicianStaffId = physician.id.toString(),
                nurseStaffId = nurse.id.toString(),
                description = "${physician.firstName} ${physician.lastName} / ${nurse.firstName} ${nurse.lastName}"
            )
        )
        coEvery { healthcareTeamService.findBy(HealthcareTeamFilters()) } returns listOf(team).success()
        coEvery {
            staffService.findByList(listOf(physician.id, nurse.id))
        } returns listOf(physician, nurse).success()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<HealthcareTeamResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getTeamsForCareCoordination should return empty list`() {
        coEvery { healthcareTeamService.findBy(HealthcareTeamFilters()) } returns emptyList<HealthcareTeam>().success()
        coEvery { staffService.findByList(emptyList()) } returns emptyList<Staff>().success()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<HealthcareTeamResponse> = response.bodyAsJson()
                assertThat(content.isEmpty())
            }
        }
    }

    @Test
    fun `#getTeamsForCareCoordination should return not found if no teams`() {
        coEvery { healthcareTeamService.findBy(HealthcareTeamFilters()) } returns NotFoundException("resource_not_found").failure()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/") { response ->
                assertThat(response).isNotFoundWithErrorCode("resource_not_found")
            }
        }
    }

    @Test
    fun `#getTeamsByIds returns error if missing parameters`() {
        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/by_staff") { response ->
                assertThat(response).isBadRequestWithErrorCode("illegal_argument")
            }
        }
        coVerify { healthcareTeamService wasNot called }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getTeamsByIds returns not found if no teams`() {
        coEvery {
            healthcareTeamService.findBy(HealthcareTeamFilters(ids = listOf(healthCareTeam.id)))
        } returns NotFoundException("resource_not_found").failure()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/by_staff?filter={id:[${healthCareTeam.id}]}") { response ->
                assertThat(response).isNotFoundWithErrorCode("resource_not_found")
            }
        }
        coVerify(exactly = 1) { healthcareTeamService.findBy(any()) }
        coVerify { staffService wasNot called }
    }

    @Test
    fun `#getTeamsByIds returns error if fails to find staff`() {
        coEvery {
            healthcareTeamService.findBy(HealthcareTeamFilters(ids = listOf(healthCareTeam.id)))
        } returns listOf(healthCareTeam).success()

        coEvery {
            staffService.findByList(healthCareTeam.staffIds)
        } returns NotFoundException("resource_not_found").failure()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/by_staff?filter={id:[${healthCareTeam.id}]}") { response ->
                assertThat(response).isNotFoundWithErrorCode("resource_not_found")
            }
        }
        coVerify(exactly = 1) { healthcareTeamService.findBy(any()) }
        coVerify(exactly = 1) { staffService.findByList(any()) }
    }

    @Test
    fun `#getTeamsByIds returns response`() {
        val expected = listOf(
            HealthcareTeamSimpleResponse(
                id = healthCareTeam.id,
                name = "Time ${physician.firstName}",
                staff = listOf(
                    StaffSimpleResponse(
                        id = physician.id,
                        firstName = physician.firstName,
                        lastName = physician.lastName,
                        profileImageUrl = physician.profileImageUrl,
                        role = physician.role,
                        roleDescription = physician.role.description,
                        fullName = physician.fullName
                    ),
                    StaffSimpleResponse(
                        id = nurse.id,
                        firstName = nurse.firstName,
                        lastName = nurse.lastName,
                        profileImageUrl = nurse.profileImageUrl,
                        role = nurse.role,
                        roleDescription = nurse.role.description,
                        fullName = nurse.fullName
                    ),
                    StaffSimpleResponse(
                        id = digitalCareNurse.id,
                        firstName = digitalCareNurse.firstName,
                        lastName = digitalCareNurse.lastName,
                        profileImageUrl = digitalCareNurse.profileImageUrl,
                        role = digitalCareNurse.role,
                        roleDescription = digitalCareNurse.role.description,
                        fullName = digitalCareNurse.fullName
                    ),
                    StaffSimpleResponse(
                        id = digitalCareNurse2.id,
                        firstName = digitalCareNurse2.firstName,
                        lastName = digitalCareNurse2.lastName,
                        profileImageUrl = digitalCareNurse2.profileImageUrl,
                        role = digitalCareNurse2.role,
                        roleDescription = digitalCareNurse2.role.description,
                        fullName = digitalCareNurse2.fullName
                    )
                )
            )
        )

        coEvery {
            healthcareTeamService.findBy(HealthcareTeamFilters(ids = listOf(healthCareTeam.id)))
        } returns listOf(healthCareTeam).success()

        coEvery {
            staffService.findByList(healthCareTeam.staffIds)
        } returns listOf(physician, nurse, digitalCareNurse, digitalCareNurse2).success()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/by_staff?filter={id:[${healthCareTeam.id}]}") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<HealthcareTeamSimpleResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
        coVerify(exactly = 1) { healthcareTeamService.findBy(any()) }
        coVerify(exactly = 1) { staffService.findByList(any()) }
    }

    @Test
    fun `#getTeamsByIds should call healthcare team only list of uuid`() {
        val expected = listOf(
            HealthcareTeamSimpleResponse(
                id = healthCareTeam.id,
                name = "Time ${physician.firstName}",
                staff = listOf(
                    StaffSimpleResponse(
                        id = physician.id,
                        firstName = physician.firstName,
                        lastName = physician.lastName,
                        profileImageUrl = physician.profileImageUrl,
                        role = physician.role,
                        roleDescription = physician.role.description,
                        fullName = physician.fullName
                    ),
                    StaffSimpleResponse(
                        id = nurse.id,
                        firstName = nurse.firstName,
                        lastName = nurse.lastName,
                        profileImageUrl = nurse.profileImageUrl,
                        role = nurse.role,
                        roleDescription = nurse.role.description,
                        fullName = nurse.fullName
                    ),
                    StaffSimpleResponse(
                        id = digitalCareNurse.id,
                        firstName = digitalCareNurse.firstName,
                        lastName = digitalCareNurse.lastName,
                        profileImageUrl = digitalCareNurse.profileImageUrl,
                        role = digitalCareNurse.role,
                        roleDescription = digitalCareNurse.role.description,
                        fullName = digitalCareNurse.fullName
                    ),
                    StaffSimpleResponse(
                        id = digitalCareNurse2.id,
                        firstName = digitalCareNurse2.firstName,
                        lastName = digitalCareNurse2.lastName,
                        profileImageUrl = digitalCareNurse2.profileImageUrl,
                        role = digitalCareNurse2.role,
                        roleDescription = digitalCareNurse2.role.description,
                        fullName = digitalCareNurse2.fullName
                    )
                )
            )
        )

        coEvery {
            healthcareTeamService.findBy(HealthcareTeamFilters(ids = listOf(healthCareTeam.id)))
        } returns listOf(healthCareTeam).success()

        coEvery {
            staffService.findByList(healthCareTeam.staffIds)
        } returns listOf(physician, nurse, digitalCareNurse, digitalCareNurse2).success()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/by_staff?filter={id:[${healthCareTeam.id}, \"on_call\"]}") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<HealthcareTeamSimpleResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
        coVerify(exactly = 1) { healthcareTeamService.findBy(any()) }
        coVerify(exactly = 1) { staffService.findByList(any()) }
    }

    @Test
    fun `#getTeamsByStaff returns response with no additional team and no multi team`() {
        val staff = TestModelFactory.buildStaff(id = testStaff.id)
        val expected = listOf(
            HealthcareTeamTypedResponse(
                id = healthCareTeam.id,
                type = HealthcareMapType.HEALTHCARE_TEAM,
                name = "Time ${physician.firstName}",
                staff = listOf(
                    StaffSimpleResponse(
                        id = physician.id,
                        firstName = physician.firstName,
                        lastName = physician.lastName,
                        profileImageUrl = physician.profileImageUrl,
                        role = physician.role,
                        roleDescription = physician.role.description,
                        fullName = physician.fullName
                    ),
                    StaffSimpleResponse(
                        id = nurse.id,
                        firstName = nurse.firstName,
                        lastName = nurse.lastName,
                        profileImageUrl = nurse.profileImageUrl,
                        role = nurse.role,
                        roleDescription = nurse.role.description,
                        fullName = nurse.fullName
                    ),
                    StaffSimpleResponse(
                        id = digitalCareNurse.id,
                        firstName = digitalCareNurse.firstName,
                        lastName = digitalCareNurse.lastName,
                        profileImageUrl = digitalCareNurse.profileImageUrl,
                        role = digitalCareNurse.role,
                        roleDescription = digitalCareNurse.role.description,
                        fullName = digitalCareNurse.fullName
                    ),
                    StaffSimpleResponse(
                        id = digitalCareNurse2.id,
                        firstName = digitalCareNurse2.firstName,
                        lastName = digitalCareNurse2.lastName,
                        profileImageUrl = digitalCareNurse2.profileImageUrl,
                        role = digitalCareNurse2.role,
                        roleDescription = digitalCareNurse2.role.description,
                        fullName = digitalCareNurse2.fullName
                    )
                )
            )
        )

        coEvery { staffService.get(staff.id) } returns staff.success()

        coEvery { healthcareTeamService.findBy(HealthcareTeamFilters(staffId = staff.id)) } returns listOf(healthCareTeam).success()

        coEvery { healthcareAdditionalTeamService.getAllHealthcareAdditionalTeamsByStaffIdAndType(staff.id, HealthcareAdditionalTeamType.REFERENCE_NURSE) } returns emptyList<HealthcareAdditionalTeam>().success()

        coEvery { personClinicalAccountService.countByMultiStaffIds(listOf(staff.id)) } returns 0.success()

        coEvery {
            healthcareTeamService.findBy(HealthcareTeamFilters(ids = listOf(healthCareTeam.id)))
        } returns listOf(healthCareTeam).success()

        coEvery {
            staffService.findByList(healthCareTeam.staffIds)
        } returns listOf(physician, nurse, digitalCareNurse, digitalCareNurse2).success()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/by_staff_v2") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<HealthcareTeamTypedResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
        coVerify(exactly = 2) { healthcareTeamService.findBy(any()) }
        coVerify(exactly = 1) { staffService.findByList(any()) }
    }

    @Test
    fun `#getTeamsByStaff returns response with additional team`() {
        val staff = TestModelFactory.buildStaff(id = testStaff.id)
        val hcAddTeam = TestModelFactory.buildHealthcareAdditionalTeam()
        val expected = listOf(
            HealthcareTeamTypedResponse(
                id = hcAddTeam.id,
                type = HealthcareMapType.ADDITIONAL_TEAM,
                name = "Time de Referência",
                staff = listOf(
                    StaffSimpleResponse(
                        id = staff.id,
                        firstName = staff.firstName,
                        lastName = staff.lastName,
                        profileImageUrl = staff.profileImageUrl,
                        role = staff.role,
                        roleDescription = staff.role.description,
                        fullName = staff.fullName
                    )
                )
            )
        )

        coEvery { staffService.get(staff.id) } returns staff.success()

        coEvery { healthcareTeamService.findBy(HealthcareTeamFilters(staffId = staff.id)) } returns emptyList<HealthcareTeam>().success()

        coEvery { healthcareAdditionalTeamService.getAllHealthcareAdditionalTeamsByStaffIdAndType(staff.id, HealthcareAdditionalTeamType.REFERENCE_NURSE) } returns listOf(hcAddTeam).success()

        coEvery { personClinicalAccountService.countByMultiStaffIds(listOf(staff.id)) } returns 0.success()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/by_staff_v2") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<HealthcareTeamTypedResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthcareTeamService.findBy(any()) }
        coVerifyOnce { healthcareAdditionalTeamService.getAllHealthcareAdditionalTeamsByStaffIdAndType(any(), any()) }
        coVerifyOnce { personClinicalAccountService.countByMultiStaffIds(any()) }
        coVerifyNone { staffService.findByList(any()) }
    }

    @Test
    fun `#getTeamsByStaff returns response with multi team`() {
        val staff = TestModelFactory.buildStaff(id = testStaff.id)
        val expected = listOf(
            HealthcareTeamTypedResponse(
                id = staff.id,
                type = HealthcareMapType.MULTI_TEAM,
                name = "Time Multi",
                staff = listOf(
                    StaffSimpleResponse(
                        id = staff.id,
                        firstName = staff.firstName,
                        lastName = staff.lastName,
                        profileImageUrl = staff.profileImageUrl,
                        role = staff.role,
                        roleDescription = staff.role.description,
                        fullName = staff.fullName
                    )
                )
            )
        )

        coEvery { staffService.get(staff.id) } returns staff.success()

        coEvery { healthcareTeamService.findBy(HealthcareTeamFilters(staffId = staff.id)) } returns emptyList<HealthcareTeam>().success()

        coEvery { healthcareAdditionalTeamService.getAllHealthcareAdditionalTeamsByStaffIdAndType(staff.id, HealthcareAdditionalTeamType.REFERENCE_NURSE) } returns emptyList<HealthcareAdditionalTeam>().success()

        coEvery { personClinicalAccountService.countByMultiStaffIds(listOf(staff.id)) } returns 1.success()

        authenticatedAs(idToken, testStaff) {
            get("/healthcare_teams/by_staff_v2") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<HealthcareTeamTypedResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }

        coVerifyOnce { staffService.get(any()) }
        coVerifyOnce { healthcareTeamService.findBy(any()) }
        coVerifyOnce { healthcareAdditionalTeamService.getAllHealthcareAdditionalTeamsByStaffIdAndType(any(), any()) }
        coVerifyOnce { personClinicalAccountService.countByMultiStaffIds(any()) }
        coVerifyNone { staffService.findByList(any()) }
    }

}
