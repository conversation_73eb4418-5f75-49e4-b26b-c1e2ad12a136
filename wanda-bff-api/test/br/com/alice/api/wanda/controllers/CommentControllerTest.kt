package br.com.alice.api.wanda.controllers

import br.com.alice.api.wanda.ControllerTestHelper
import br.com.alice.api.wanda.model.Comment
import br.com.alice.api.wanda.model.WandaCommentRequest
import br.com.alice.api.wanda.model.WandaCommentResponse
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.staff.client.StaffService
import br.com.alice.wanda.client.WandaCommentService
import br.com.alice.wanda.model.WandaCommentTransport
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class CommentControllerTest : ControllerTestHelper() {

    private val wandaCommentService: WandaCommentService = mockk()
    private val staffService: StaffService = mockk()
    private val comment = TestModelFactory.buildWandaComment(staffId = testStaff.id)

    private val request = WandaCommentRequest(
        comment = comment.comment
    )
    private val expected = WandaCommentResponse(
        personHealthEventId = comment.personHealthEventId.toString(),
        comments = listOf(
            Comment(
                id = comment.id,
                comment = comment.comment,
                addedAt = comment.addedAt.toString(),
                staff = staff
            )
        )
    )
    private val transport = WandaCommentTransport(
        comment = comment.comment,
        personHealthEventId = comment.personHealthEventId,
        staffId = comment.staffId,
        personId = comment.personId
    )

    @BeforeTest
    override fun setup() {
        super.setup()
        clearAllMocks()
        module.single { CommentController(wandaCommentService, staffService) }
    }

    @Test
    fun `#create create and return`() {
        coEvery { wandaCommentService.create(transport) } returns comment.success()

        coEvery {
            staffService.get(testStaff.id)
        } returns staff.success()

        authenticatedAs(idToken, testStaff) {
            post(
                to = "/person_health_events/${comment.personId}/${comment.personHealthEventId}/comment",
                body = request
            ) { response ->
                assertThat(response).isSuccessfulJson()
                val content: WandaCommentResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getByTaskId returns error`() {
        coEvery {
            wandaCommentService.getByTaskId(comment.personHealthEventId)
        } returns NotFoundException().failure()

        authenticatedAs(idToken, testStaff) {
            get(
                url = "/person_health_events/${comment.personId}/${comment.personHealthEventId}/comments",
            ) { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `#getByTaskId returns list`() {
        coEvery {
            wandaCommentService.getByTaskId(comment.personHealthEventId)
        } returns listOf(comment).success()

        coEvery {
            staffService.findByList(listOf(testStaff.id))
        } returns listOf(staff).success()

        authenticatedAs(idToken, testStaff) {
            get(
                url = "/person_health_events/${comment.personId}/${comment.personHealthEventId}/comments",
            ) { response ->
                assertThat(response).isSuccessfulJson()
                val content: WandaCommentResponse = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
    }
}
