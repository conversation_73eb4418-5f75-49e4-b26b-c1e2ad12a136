package br.com.alice.api.wanda.controllers

import br.com.alice.api.wanda.ControllerTestHelper
import br.com.alice.channel.client.ChannelFupService
import br.com.alice.channel.models.ChannelFollowUpResponse
import br.com.alice.common.core.Status
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.data.dsl.matchers.ResponseAssert.Companion.assertThat
import br.com.alice.common.helpers.bodyAsJson
import br.com.alice.data.layer.models.ChannelFollowUpOptions
import br.com.alice.data.layer.models.ChannelFup
import br.com.alice.data.layer.models.ChannelFupAnswer
import br.com.alice.staff.client.StaffService
import br.com.alice.wanda.client.AutomaticFollowUpService
import com.github.kittinunf.result.failure
import com.github.kittinunf.result.success
import io.mockk.clearAllMocks
import io.mockk.coEvery
import io.mockk.mockk
import org.assertj.core.api.Assertions.assertThat
import kotlin.test.BeforeTest
import kotlin.test.Test

class AutomaticFollowUpControllerTest : ControllerTestHelper() {

    private val channelFupService: ChannelFupService = mockk()
    private val automaticFollowUpService: AutomaticFollowUpService = mockk()
    private val staffService: StaffService = mockk()

    @BeforeTest
    override fun setup() {
        super.setup()
        clearAllMocks()
        module.single { AutomaticFollowUpController(channelFupService, automaticFollowUpService, staffService) }
    }

    @Test
    fun `#getAutomaticFollowUps returns all follow ups`() {
        val channelFups = listOf(
            ChannelFup(
                name = "Pressão Alta",
                question = "Como você está se sentindo em relação a sua pressão alta?",
                answers = listOf(
                    ChannelFupAnswer(
                        label = "Sim",
                        key = "i_feel_better"
                    )
                ),
                status = Status.ACTIVE
            )
        )
        val expected = listOf(
            ChannelFollowUpResponse(
                channelFupId = channelFups.first().id,
                description = "Pressão Alta",
                question = "Como você está se sentindo em relação a sua pressão alta?",
                answers = listOf(
                    ChannelFollowUpOptions(
                        icon = channelFups.first().answers.first().icon,
                        label = "Sim",
                        value = "i_feel_better"
                    )
                )
            )
        )
        coEvery { channelFupService.getAll(onlyActive = true) } returns channelFups.success()

        authenticatedAs(idToken, testStaff) {
            get("/automatic_follow_ups/") { response ->
                assertThat(response).isSuccessfulJson()
                val content: List<ChannelFollowUpResponse> = response.bodyAsJson()
                assertThat(content).isEqualTo(expected)
            }
        }
    }

    @Test
    fun `#getAutomaticFollowUps returns error`() {
        coEvery { channelFupService.getAll(onlyActive = true) } returns NotFoundException().failure()

        authenticatedAs(idToken, testStaff) {
            get("/automatic_follow_ups/") { response ->
                assertThat(response).isNotFound()
            }
        }
    }

    @Test
    fun `#executeAutoFollowUps should execute`() {
        coEvery { automaticFollowUpService.publishAutomaticFollowUps() } returns true.success()

        authenticatedAs(idToken, testStaff) {
            post("/automatic_follow_ups/execute/") { response ->
                assertThat(response).isSuccessfulJson()
                val content: Boolean = response.bodyAsJson()
                assertThat(content).isTrue()
            }
        }
    }
}
