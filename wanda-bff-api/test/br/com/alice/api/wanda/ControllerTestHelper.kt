package br.com.alice.api.wanda

import br.com.alice.common.core.Role
import br.com.alice.common.core.User
import br.com.alice.common.helpers.RoutesTestHelper
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.Staff
import br.com.alice.featureconfig.core.FeaturePopulateService
import io.ktor.server.application.Application
import io.mockk.mockk
import org.koin.core.context.loadKoinModules
import org.koin.dsl.module

abstract class ControllerTestHelper : RoutesTestHelper() {

    private val featurePopulateService: FeaturePopulateService = mockk()

    override val setupFunction: Application.() -> Unit = { module(dependencyInjectionModules = listOf(module)) }

    override val moduleFunction: Application.() -> Unit = {
        loadKoinModules(
            listOf(
                module,
                module(createdAtStart = true) { single { featurePopulateService } }
            )
        )
    }

    protected val idToken = "idToken"
    protected val staff = TestModelFactory.buildStaff()
    protected val testStaff = toTestUser(staff)

    protected fun toTestUser(staff: Staff) = br.com.alice.common.models.Staff(
        email = staff.email,
        id = staff.id,
        role = staff.role.let { Role.valueOf(staff.role.name) }
    )

    // Random string to represent generated Firebase uid
    private val charPool: List<Char> = ('a'..'z') + ('A'..'Z') + ('0'..'9')
    override fun uid(user: User) =
        (1..10).map { kotlin.random.Random.nextInt(0, charPool.size) }.map(charPool::get).joinToString("")

}
