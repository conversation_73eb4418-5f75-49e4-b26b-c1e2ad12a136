package br.com.alice.acquisition.services.internal

import br.com.alice.acquisition.converters.AssociationBuilder
import br.com.alice.acquisition.model.CommercialRepresentative
import br.com.alice.acquisition.model.Metadata
import br.com.alice.acquisition.model.Offer
import br.com.alice.acquisition.model.OfferStage
import br.com.alice.acquisition.model.PotentialCustomer
import br.com.alice.common.RangeUUID
import br.com.alice.common.core.extensions.atBeginningOfTheDay
import br.com.alice.common.core.exceptions.NotFoundException
import br.com.alice.common.helpers.MockedTestHelper
import br.com.alice.common.helpers.coVerifyNone
import br.com.alice.common.helpers.coVerifyOnce
import br.com.alice.communication.crm.sales.b2b.AssociationResultV2
import br.com.alice.communication.crm.sales.b2b.CompanyResultV2
import br.com.alice.communication.crm.sales.b2b.DealResultV2
import br.com.alice.communication.crm.sales.b2b.DealStageV2
import br.com.alice.communication.crm.sales.b2b.DealV2
import br.com.alice.communication.crm.sales.b2b.HubspotSalesFunnelPipeline
import br.com.alice.hubspot.integration.lib.clients.HubspotClient
import br.com.alice.hubspot.integration.lib.models.Contact
import br.com.alice.hubspot.integration.lib.models.ContactResponse
import br.com.alice.hubspot.integration.lib.models.Deal
import br.com.alice.hubspot.integration.lib.models.DealResponse
import com.github.kittinunf.result.getOrNull
import io.mockk.coEvery
import io.mockk.mockk
import java.util.UUID
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals

class PipelineServiceImplTest: MockedTestHelper() {
    private val hubspotSalesFunnelPipeline: HubspotSalesFunnelPipeline = mockk()
    private val hubspotClient: HubspotClient = mockk()
    private val pipelineServiceImpl = PipelineServiceImpl(hubspotSalesFunnelPipeline, hubspotClient)

    private val commercialRepresentative = CommercialRepresentative(
        email = "email",
        firstName = "firstName",
        phoneNumber = "phoneNumber",
        postalCode = "postalCode",
    )

    private val potentialCustomer = PotentialCustomer(
        leadId = RangeUUID.generate(),
        documentNumber = "12345678901234",
        name = "Alice",
        city = "Cotia",
        employeesNumber = "1-5",
        additionalProperties = mapOf(
            PotentialCustomer.PIPELINE_COMMERCIAL_REPRESENTATIVE_EXTERNAL_ID_KEY to "commercialRepresentativeExternalId",
            PotentialCustomer.PIPELINE_EXTERNAL_ID_KEY to "pipelineExternalId",
        )
    )

    private val metadata = Metadata(
        utmSource = "utmSource",
        utmMedium = "utmMedium",
        utmCampaignName = "utmCampaignName",
        utmTerm = "utmTerm",
        utmContent = "utmContent",
        utmAdId = "utmAdId",
        utmAdSet = "utmAdSet",
        utmAdSetId = "utmAdSetId",
        utmCampaign = "utmCampaign",
        utmCampaignId = "utmCampaignId",
        utmBlog = "utmBlog",
        gclid = "gclid",
    )

    @Test
    fun `#getOrCreateCommercialRepresentative should return a contact when it exists`() = runBlocking {
        coEvery {
            hubspotClient.getContactByEmail(commercialRepresentative.email)
        } returns ContactResponse("hs_object_id")

        val result = pipelineServiceImpl.getOrCreatePipelineCommercialRepresentativeId(commercialRepresentative, metadata)
        assertEquals("hs_object_id", result.getOrNull())

        coVerifyOnce { hubspotClient.getContactByEmail(any()) }
        coVerifyNone { hubspotClient.createContact(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.getContactWithAllInformationByEmail(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.createNewContact(any()) }
    }

    @Test
    fun `#getOrCreateCommercialRepresentative should create a contact when it does not exists`() = runBlocking {
        val contact = Contact(
            email = commercialRepresentative.email,
            firstname = commercialRepresentative.firstName,
            lastname = commercialRepresentative.lastName,
            nickname = commercialRepresentative.firstName,
            phone = commercialRepresentative.phoneNumber,
            nationalId = commercialRepresentative.nationalId,
            dateOfBirth = commercialRepresentative.dateOfBirth,
            utmSource = metadata.utmSource,
            utmMedium = metadata.utmMedium,
            utmCampaignName = metadata.utmCampaignName,
            utmTerm = metadata.utmTerm,
            utmContent = metadata.utmContent,
            utmAdId = metadata.utmAdId,
            utmAdSet = metadata.utmAdSet,
            utmAdSetId = metadata.utmAdSetId,
            utmCampaign = metadata.utmCampaign,
            utmCampaignId = metadata.utmCampaignId,
            utmBlog = metadata.utmBlog,
            hsGoogleClickId = metadata.gclid,
        )

        coEvery {
            hubspotClient.getContactByEmail(commercialRepresentative.email)
        } returns null
        coEvery {
            hubspotClient.createContact(contact)
        } returns ContactResponse("hs_object_id")

        val result = pipelineServiceImpl.getOrCreatePipelineCommercialRepresentativeId(commercialRepresentative, metadata)
        assertEquals("hs_object_id", result.getOrNull())

        coVerifyOnce { hubspotClient.getContactByEmail(any()) }
        coVerifyOnce { hubspotClient.createContact(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.getContactWithAllInformationByEmail(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.createNewContact(any()) }
    }

    @Test
    fun `#getOrCreateCommercialRepresentative should return failure when fail to get contact info`() = runBlocking {
        coEvery {
            hubspotClient.getContactByEmail(commercialRepresentative.email)
        } throws Exception("test")

        val result = pipelineServiceImpl.getOrCreatePipelineCommercialRepresentativeId(commercialRepresentative, metadata)
        assertEquals("test", result.failure().message)

        coVerifyOnce { hubspotClient.getContactByEmail(any()) }
        coVerifyNone { hubspotClient.createContact(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.getContactWithAllInformationByEmail(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.createNewContact(any()) }
    }

    @Test
    fun `#getOrCreateCommercialRepresentative should return failure when fail to create contact`() = runBlocking {
        val contact = Contact(
            email = commercialRepresentative.email,
            firstname = commercialRepresentative.firstName,
            lastname = commercialRepresentative.lastName,
            nickname = commercialRepresentative.firstName,
            phone = commercialRepresentative.phoneNumber,
            nationalId = commercialRepresentative.nationalId,
            dateOfBirth = commercialRepresentative.dateOfBirth,
            utmSource = metadata.utmSource,
            utmMedium = metadata.utmMedium,
            utmCampaignName = metadata.utmCampaignName,
            utmTerm = metadata.utmTerm,
            utmContent = metadata.utmContent,
            utmAdId = metadata.utmAdId,
            utmAdSet = metadata.utmAdSet,
            utmAdSetId = metadata.utmAdSetId,
            utmCampaign = metadata.utmCampaign,
            utmCampaignId = metadata.utmCampaignId,
            utmBlog = metadata.utmBlog,
            hsGoogleClickId = metadata.gclid,
        )

        coEvery {
            hubspotClient.getContactByEmail(commercialRepresentative.email)
        } returns null
        coEvery {
            hubspotClient.createContact(contact)
        } throws Exception("test")

        val result = pipelineServiceImpl.getOrCreatePipelineCommercialRepresentativeId(commercialRepresentative, metadata)
        assertEquals("test", result.failure().message)

        coVerifyOnce { hubspotClient.getContactByEmail(any()) }
        coVerifyOnce { hubspotClient.createContact(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.getContactWithAllInformationByEmail(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.createNewContact(any()) }
    }

    @Test
    fun `#createPotentialCustomer should create a company successfully`() = runBlocking {
        coEvery {
            hubspotSalesFunnelPipeline.createCompany(potentialCustomer.toCompanyV2())
        } returns CompanyResultV2(
            id = "pipeId",
        )

        val result = pipelineServiceImpl.createPotentialCustomer(potentialCustomer)

        assertEquals("pipeId", result.getOrNull()?.id)

        coVerifyOnce { hubspotSalesFunnelPipeline.createCompany(any()) }
    }

    @Test
    fun `#updatePotentialCustomer should update a company successfully`() = runBlocking {
        coEvery {
            hubspotSalesFunnelPipeline.updateCompany(
                potentialCustomer.additionalProperties[PotentialCustomer.PIPELINE_EXTERNAL_ID_KEY]!!,
                potentialCustomer.toCompanyV2(),
            )
        } returns CompanyResultV2(
            id = "pipeId",
        )

        val result = pipelineServiceImpl.updatePotentialCustomer(potentialCustomer)

        assertEquals("pipeId", result.getOrNull()?.id)

        coVerifyOnce { hubspotSalesFunnelPipeline.updateCompany(any(), any()) }
    }

    @Test
    fun `#upsertPotentialCustomer should create a company successfully`() = runBlocking {
        coEvery {
            hubspotSalesFunnelPipeline.findCompanyByCnpj(potentialCustomer.documentNumber)
        } returns null
        coEvery {
            hubspotSalesFunnelPipeline.createCompany(potentialCustomer.toCompanyV2())
        } returns CompanyResultV2(
            id = "pipeId",
        )

        val result = pipelineServiceImpl.upsertPotentialCustomer(potentialCustomer)

        assertEquals("pipeId", result.getOrNull()?.id)

        coVerifyOnce { hubspotSalesFunnelPipeline.findCompanyByCnpj(any()) }
        coVerifyOnce { hubspotSalesFunnelPipeline.createCompany(any()) }
    }

    @Test
    fun `#upsertPotentialCustomer update a company successfully`() = runBlocking {
        coEvery {
            hubspotSalesFunnelPipeline.findCompanyByCnpj(potentialCustomer.documentNumber)
        } returns CompanyResultV2(
            id = "pipeId",
        )
        coEvery {
            hubspotSalesFunnelPipeline.updateCompany(
                "pipeId",
                potentialCustomer.toCompanyV2(),
            )
        } returns CompanyResultV2(
            id = "pipeId",
        )

        val result = pipelineServiceImpl.upsertPotentialCustomer(potentialCustomer)

        assertEquals("pipeId", result.getOrNull()?.id)

        coVerifyOnce { hubspotSalesFunnelPipeline.findCompanyByCnpj(any()) }
        coVerifyOnce { hubspotSalesFunnelPipeline.updateCompany(any(), any()) }
    }

    @Test
    fun `#upsertPotentialCustomer return failure when exception occurs`() = runBlocking {
        coEvery {
            hubspotSalesFunnelPipeline.findCompanyByCnpj(potentialCustomer.documentNumber)
        } throws Exception("test")

        val result = pipelineServiceImpl.upsertPotentialCustomer(potentialCustomer)

        assertEquals("test", result.failure().message)

        coVerifyOnce { hubspotSalesFunnelPipeline.findCompanyByCnpj(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.createCompany(any()) }
        coVerifyNone { hubspotSalesFunnelPipeline.updateCompany(any(), any()) }
    }

    @Test
    fun `#associateToCommercialRepresentative should update a company successfully`() = runBlocking {
        coEvery {
            hubspotSalesFunnelPipeline.associateContactToCompany(
                contactId = potentialCustomer.additionalProperties[PotentialCustomer.PIPELINE_COMMERCIAL_REPRESENTATIVE_EXTERNAL_ID_KEY]!!,
                companyId = potentialCustomer.additionalProperties[PotentialCustomer.PIPELINE_EXTERNAL_ID_KEY]!!,
            )
        } returns AssociationResultV2(true)

        pipelineServiceImpl.associateToCommercialRepresentative(potentialCustomer)

        coVerifyOnce { hubspotSalesFunnelPipeline.associateContactToCompany(any(), any()) }
    }

    @Test
    fun `createOffer should return correct DealResultV2 when service returns valid data`() = runBlocking {
        val offer = Offer()

        val expectedResponse = DealResultV2(offer.id.toString())

        coEvery { hubspotSalesFunnelPipeline.createDeal(offer.toDealV2(metadata)) } returns expectedResponse

        val actualResponse = pipelineServiceImpl.createOffer(offer, metadata)

        assertEquals(expectedResponse, actualResponse.getOrNull())
    }

    @Test
    fun `associateContactToOffer should return correct AssociationResultV2 when service returns valid data`() = runBlocking {
        val offer = Offer(
            additionalProperties = potentialCustomer.additionalProperties.plus(
            Offer.PIPELINE_EXTERNAL_ID_KEY to "pipeId"
            ). plus(
            Offer.PIPELINE_EXTERNAL_CONTACT_ID_KEY to "contactId"
            )
        )

        val associations = AssociationBuilder.buildContactToDealAssociation(
            contactId = offer.additionalProperties[Offer.PIPELINE_EXTERNAL_CONTACT_ID_KEY]!!,
            dealId = offer.additionalProperties[Offer.PIPELINE_EXTERNAL_ID_KEY]!!,
        )

        coEvery { hubspotClient.associateContactToDeal(associations) } returns Unit

        val actualResponse = pipelineServiceImpl.associateContactToOffer(offer)

        assertEquals(Unit, actualResponse.getOrNull())
    }

    @Test
    fun `searchOffersByCnpj should return a list of incomplete Offer when service returns valid data`() = runBlocking {
        val cnpj = "12345678901234"

        val mockCompanyDeal = Deal(
            pipeline = "1",
            dealstage = "*********",
            b2b_company_id_aos = RangeUUID.generate().toString(),
            empresa_cnpj = "4",
            dealname = "5",
            deal_email = "6",
            deal_phone = "7",
            zip_code = "8",
            n3p_corretora = "9",
            n3p__cpf_do_corretor = "10",
            link_do_boleto_empresarial = "11",
            b2b_membros_no_contrato = "12",
            b2b__razao_social_da_empresa = "13",
            b2b_total_de_funcionarios = "14",
            b2b__data_de_ativacao = "15",
            b2b__email_representante_legal = "16"
        )

        val mockResponse = DealResponse(
            id = RangeUUID.generate().toString(),
            properties = mockCompanyDeal
        )

        val expectedResponse = listOf(
            Offer(
                id = UUID.fromString(mockResponse.id),
                name = mockCompanyDeal.dealname,
                email = mockCompanyDeal.deal_email,
                phoneNumber = mockCompanyDeal.deal_phone,
                companyCnpj = mockCompanyDeal.empresa_cnpj,
                companyId = mockCompanyDeal.b2b_company_id_aos,
                stage = OfferStage.DEAL_CREATED,
                additionalProperties = mapOf(
                    Offer.PIPELINE_EXTERNAL_ID_KEY to mockResponse.id,
                )
            )
        )

        coEvery { hubspotClient.searchAllDealsByCnpj(cnpj) } returns listOf(mockResponse)

        val actualResponse = pipelineServiceImpl.searchOffersByCnpj(cnpj).get()

        assertEquals(actualResponse.size, 1)
        assertEquals(
            expectedResponse.first().additionalProperties[Offer.PIPELINE_EXTERNAL_ID_KEY], 
            actualResponse.first().additionalProperties[Offer.PIPELINE_EXTERNAL_ID_KEY]
        )

        coVerifyOnce { hubspotClient.searchAllDealsByCnpj(cnpj) }
    }

    @Test
    fun `searchOffersByCnpj should return empty list when service returns empty list`() = runBlocking {
        val cnpj = "12345678901234"

        coEvery { hubspotClient.searchAllDealsByCnpj(cnpj) } returns emptyList()

        val actualResponse = pipelineServiceImpl.searchOffersByCnpj(cnpj)

        assertEquals(listOf<Offer>(), actualResponse.get())
        coVerifyOnce { hubspotClient.searchAllDealsByCnpj(cnpj) }
    }

    @Test
    fun `searchOffersByCnpj should return Throwable when service throws NotFoundException`() = runBlocking {
        val cnpj = "12345678901234"

        coEvery { hubspotClient.searchAllDealsByCnpj(cnpj) } throws NotFoundException("test")

        val actualResponse = pipelineServiceImpl.searchOffersByCnpj(cnpj)

        assertEquals("test", actualResponse.failure().message)
        coVerifyOnce { hubspotClient.searchAllDealsByCnpj(cnpj) }
    }

    private fun Offer.toDealV2(metadata: Metadata): DealV2 =
        DealV2(
            name = this.name,
            email = this.email,
            stage = DealStageV2.LEAD,
            declaredAge = this.declaredAge,
            utmSource = metadata.utmSource,
            utmMedium = metadata.utmMedium,
            utmCampaignName = metadata.utmCampaignName,
            utmCampaign = metadata.utmCampaign,
            utmCampaignId = metadata.utmCampaignId,
            utmAdSet = metadata.utmAdSet,
            utmAdSetId = metadata.utmAdSetId,
            utmAdId = metadata.utmAdId,
            utmTerm = metadata.utmTerm,
            utmContent = metadata.utmContent,
            simulationLink = this.simulationLink,
            latestSimulation = this.latestSimulation?.toLocalDate()?.atBeginningOfTheDay(),
            leadId = this.leadId.toString(),
            simulationId = this.simulationId.toString(),
            utmBlog = metadata.utmBlog,
            findOutAlice = this.findOutAlice,
            companyCnpj = this.companyCnpj,
            companyId = this.companyId,
            phoneNumber = this.phoneNumber,
            companyCity = this.companyCity,
            employeesQty = this.employeesQty,
            b2b__tipo_de_lead = this.hubspotLeadType,
        )
}
