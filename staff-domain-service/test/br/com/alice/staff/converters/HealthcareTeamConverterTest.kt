package br.com.alice.staff.converters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.HealthcareTeam
import br.com.alice.data.layer.models.HealthcareTeamModel
import kotlin.test.Test
import kotlin.test.assertEquals

class HealthcareTeamConverterTest {

    private val healthcareTeam = HealthcareTeam(
        physicianStaffId = RangeUUID.generate(),
        nurseStaffId = RangeUUID.generate(),
        digitalCareNurseStaffIds = listOf(RangeUUID.generate()),
        careCoordNurseStaffId = RangeUUID.generate(),
        active = true,
        type = HealthcareTeam.Type.STANDARD,
        segment = HealthcareTeam.Segment.DEFAULT,
        maxMemberAssociation = 1,
        address = TestModelFactory.buildStructuredAddress(),
        id = RangeUUID.generate(),
        version = 1,
    )

    private val healthcareTeamModel = HealthcareTeamModel(
        physicianStaffId = healthcareTeam.physicianStaffId,
        nurseStaffId = healthcareTeam.nurseStaffId,
        digitalCareNurseStaffIds = healthcareTeam.digitalCareNurseStaffIds,
        careCoordNurseStaffId = healthcareTeam.careCoordNurseStaffId,
        active = healthcareTeam.active,
        type = HealthcareTeamModel.Type.STANDARD,
        segment = HealthcareTeamModel.Segment.DEFAULT,
        maxMemberAssociation = healthcareTeam.maxMemberAssociation,
        address = healthcareTeam.address,
        id = healthcareTeam.id,
        version = healthcareTeam.version,
        createdAt = healthcareTeam.createdAt,
        updatedAt = healthcareTeam.updatedAt,
    )

    @Test
    fun testToModel() {
        assertEquals(healthcareTeamModel, healthcareTeam.toModel())
    }

    @Test
    fun testToTransport() {
        assertEquals(healthcareTeam, healthcareTeamModel.toTransport())
    }
}
