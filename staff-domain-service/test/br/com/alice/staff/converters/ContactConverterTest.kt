package br.com.alice.staff.converters

import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.AvailableDay
import br.com.alice.data.layer.models.AvailableDayModel
import br.com.alice.data.layer.models.Contact
import br.com.alice.data.layer.models.ContactModel
import br.com.alice.data.layer.models.ContactWeekday
import br.com.alice.data.layer.models.ModalityType
import br.com.alice.data.layer.models.PhoneNumber
import kotlin.test.Test
import kotlin.test.assertEquals

class ContactConverterTest {

    private val contact = Contact(
        id = RangeUUID.generate(),
        version = 1,
        addressId = RangeUUID.generate(),
        website = "www.com.br",
        phones = listOf(PhoneNumber(phone = "123456789")),
        modality = ModalityType.PRESENTIAL,
        scheduleAvailabilityDays = 1,
        availableDays = listOf(AvailableDay(weekDay = ContactWeekday.FRIDAY)),
        address = TestModelFactory.buildStructuredAddress()
    )

    private val contactModel = ContactModel(
        id = contact.id,
        version = contact.version,
        addressId = contact.addressId,
        website = contact.website,
        phones = contact.phones,
        modality = ModalityType.PRESENTIAL,
        scheduleAvailabilityDays = contact.scheduleAvailabilityDays,
        availableDays = listOf(AvailableDayModel(weekDay = ContactWeekday.FRIDAY)),
        address = contact.address,
        createdAt = contact.createdAt,
        updatedAt = contact.updatedAt,
    )

    @Test
    fun testToModel() {
        assertEquals(contactModel, contact.toModel())
    }

    @Test
    fun testToTransport() {
        assertEquals(contact, contactModel.toTransport())
    }
}
