package br.com.alice.api.backoffice.mappers.staff

import br.com.alice.api.backoffice.transfers.staff.StaffFullResponse
import br.com.alice.api.backoffice.transfers.staff.StaffShortResponse
import org.assertj.core.api.Assertions.assertThat
import br.com.alice.data.layer.helpers.TestModelFactory
import io.ktor.http.Parameters
import java.time.LocalDate
import kotlin.test.Test

class StaffOutputMapperTest {

    private val staff = TestModelFactory.buildStaff()

    @Test
    fun `#toPaginatedResponse returns StaffShortResponse based on Staff`() {
        val total = 10
        val params = Parameters.build {
            append("page", "1")
            append("pageSize", "3")
        }
        val response = StaffShortResponse(
            id = staff.id,
            firstName = staff.firstName,
            lastName = staff.lastName,
            role = staff.role,
            email = staff.email,
            active = staff.active
        )
        val expected = listOf(response, response, response)

        val result = StaffOutputMapper.toPaginatedResponse(listOf(staff, staff, staff), total, params)
        assertThat(result.results).isEqualTo(expected)
        assertThat(result.pagination.totalPages).isEqualTo(4)
        assertThat(result.pagination.page).isEqualTo(1)
        assertThat(result.pagination.pageSize).isEqualTo(3)
    }

    @Test
    fun `#toFullResponse returns StaffFullResponse based on Staff`() {
        val expected = StaffFullResponse(
            id = staff.id,
            firstName = staff.firstName,
            lastName = staff.lastName,
            fullName = staff.fullName,
            role = staff.role,
            email = staff.email,
            active = staff.active,
            nationalId = staff.nationalId,
            type = staff.type,
            gender = staff.gender,
            version = staff.version,
            birthDate = staff.birthdate,
            profileImageUrl = staff.profileImageUrl
        )

        val result = StaffOutputMapper.toFullResponse(staff)
        assertThat(result).isEqualTo(expected)
    }
}
