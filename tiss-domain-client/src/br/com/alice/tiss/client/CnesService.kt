package br.com.alice.tiss.client

import br.com.alice.common.rfc.RemoteService
import br.com.alice.common.rfc.Service
import com.github.kittinunf.result.Result

@RemoteService
interface CnesService : Service {
    override val namespace get() = "tiss"
    override val serviceName get() = "cnes-webservice"

    suspend fun getCnesByNationalId(
        nationalId: String
    ): Result<String, Throwable>

}
