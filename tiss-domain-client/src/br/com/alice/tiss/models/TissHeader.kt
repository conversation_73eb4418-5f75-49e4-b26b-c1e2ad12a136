package br.com.alice.tiss.models

import br.com.alice.exec.indicator.models.GuiaOrigin
import br.com.alice.tiss.utils.TissTransactionTypeEnum
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId

data class TissHeaderParams(
    val tissVersion: String,
    val providerCnpj: String,
    val transactionType: TissTransactionTypeEnum,
    val aliceANSCode: String = "421928",
    val date: String = LocalDate.now().toString(),
    val time: String = LocalTime.now(ZoneId.of("America/Sao_Paulo")).toString(),
    val origin: GuiaOrigin? = null
)
