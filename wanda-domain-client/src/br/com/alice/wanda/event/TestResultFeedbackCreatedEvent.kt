package br.com.alice.wanda.event

import br.com.alice.common.notification.NotificationEvent
import br.com.alice.data.layer.models.TestResultFeedback
import br.com.alice.wanda.SERVICE_NAME

class TestResultFeedbackCreatedEvent(testResultFeedback: TestResultFeedback) :
    NotificationEvent<TestResultFeedbackPayload>(
        name = name,
        producer = SERVICE_NAME,
        payload = TestResultFeedbackPayload(testResultFeedback = testResultFeedback)
    ) {
    companion object {
        const val name = "test-result-feedback"
    }
}

data class TestResultFeedbackPayload(
    val testResultFeedback: TestResultFeedback
)
