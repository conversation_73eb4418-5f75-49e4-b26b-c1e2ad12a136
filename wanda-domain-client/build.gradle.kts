plugins {
    kotlin
    `kotlin-kapt`
}

kapt {
    correctErrorTypes = false
    generateStubs = false
    includeCompileClasspath = false
    useBuildCache = true
}

group = "br.com.alice.wanda-domain-client"
version = aliceWandaDomainClientVersion

sourceSets {
    main {
        kotlin.sourceDirs = files("src")
        resources.sourceDirs = files("resources")
    }
    test {
        kotlin.sourceDirs = files("test")
        resources.sourceDirs = files("testResources")
    }
}

val api by configurations

dependencies {
    implementation(project(":common"))
    implementation(project(":data-layer-client"))
	implementation(project(":data-packages:staff-domain-service-data-package"))
	implementation(project(":data-packages:person-domain-service-data-package"))
	implementation(project(":data-packages:secondary-attention-domain-service-data-package"))
	implementation(project(":data-packages:dragon-radar-domain-service-data-package"))
	implementation(project(":data-packages:appointment-domain-service-data-package"))
	implementation(project(":data-packages:wanda-domain-service-data-package"))

    kapt(project(":common"))
    ktor2Dependencies()
    

    testImplementation(project(":common-tests"))
    testImplementation(project(":data-layer-common-tests"))

    test2Dependencies()
}
