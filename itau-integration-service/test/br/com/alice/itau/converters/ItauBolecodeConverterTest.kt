package br.com.alice.itau.converters

import br.com.alice.common.PaymentMethod
import br.com.alice.common.RangeUUID
import br.com.alice.data.layer.helpers.TestModelFactory
import br.com.alice.data.layer.models.BillingAccountablePartyType
import br.com.alice.itau.ServiceConfig
import br.com.alice.itau.clients.models.ItauBolecodeBoletoListCreateResponse
import br.com.alice.itau.clients.models.ItauBolecodeBoletoResponse
import br.com.alice.itau.clients.models.ItauBolecodeCreateDataResponse
import br.com.alice.itau.clients.models.ItauBolecodePixResponse
import br.com.alice.itau.clients.models.ItauBoletoPayerPersonCode
import br.com.alice.itau.converter.toAcquirerCreatePaymentResponse
import br.com.alice.itau.converter.toItauBolecodeCreateRequest
import br.com.alice.itau.converter.toItauBoletoValue
import br.com.alice.moneyin.models.PaymentRequestInput
import br.com.alice.moneyin.models.PaymentStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate

class ItauBolecodeConverterExtraTest {

    private val itauPayment = TestModelFactory.buildItauPayment()

    @Test
    fun `#toItauBoletoValue should format BigDecimal into 17-digit string`() {
        val amount = BigDecimal("123.45")
        val expected = "00000000000012345"

        val result = amount.toItauBoletoValue()

        assertThat(result).isEqualTo(expected)
    }

    @Test
    fun `#toAcquirerCreatePaymentResponse should convert ItauBolecodeCreateDataResponse to AcquirerCreatePaymentResponse`() {
        val response = ItauBolecodeCreateDataResponse(
            boleto = ItauBolecodeBoletoListCreateResponse(
                individualBoleto = listOf(
                    ItauBolecodeBoletoResponse(
                        id = "boletoId",
                        barcode = "barcode123",
                        digitableLine = "**********"
                    )
                )
            ),
            pix = ItauBolecodePixResponse(
                id = "pixId",
                copyAndPaste = "copy",
                qrCodeBase64 = "base64"
            )
        )

        val (convertedResponse, externalIds) = response.toAcquirerCreatePaymentResponse(itauPayment)

        assertThat(convertedResponse.status).isEqualTo(PaymentStatus.PENDING)
        assertThat(convertedResponse.pix?.copyAndPaste).isEqualTo("copy")
        assertThat(convertedResponse.pix?.qrCodeBase64).isEqualTo("base64")
        assertThat(convertedResponse.bankSlip?.barcodeData).isEqualTo("barcode123")
        assertThat(convertedResponse.bankSlip?.digitableLine).isEqualTo("**********")
        assertThat(externalIds.pixId).isEqualTo("pixId")
        assertThat(externalIds.boletoId).isEqualTo("boletoId")
    }

    @Test
    fun `#toItauBolecodeCreateRequest should convert PaymentRequestInput to ItauBolecodeCreateRequest`() {
        val payer = PaymentRequestInput.Payer(
            id = RangeUUID.generate(),
            nationalId = "**************",
            name = "Empresa Ltda",
            address = null,
            email = "<EMAIL>",
            type = BillingAccountablePartyType.LEGAL_PERSON
        )

        val paymentRequestInput = PaymentRequestInput(
            orderId = RangeUUID.generate(),
            referenceDates = emptyList(),
            dueDate = LocalDate.of(2025, 5, 15),
            payer = payer,
            people = emptyList(),
            isFirstPayment = false,
            method = PaymentMethod.BOLEPIX,
            installment = 0,
            totalInstallments = 1,
            totalAmount = BigDecimal("150.75")
        )

        val request = paymentRequestInput.toItauBolecodeCreateRequest(itauPayment)

        assertThat(request.beneficiary.aliceBankAccountId).isEqualTo(ServiceConfig.itauBoletoConfig.aliceAccountKey)
        assertThat(request.boletoData.qrCode.alicePixKey).isEqualTo(ServiceConfig.itauPixConfig.alicePixKey)
        assertThat(request.boletoData.payer.person.name).isEqualTo("Empresa Ltda")
        assertThat(request.boletoData.payer.person.personType.cnpj).isEqualTo("**************")
        assertThat(request.boletoData.payer.person.personType.personCode).isEqualTo(ItauBoletoPayerPersonCode.JURIDICA.code)
        assertThat(request.boletoData.individualBoleto.first().dueDate).isEqualTo("2025-05-15")
        assertThat(request.boletoData.individualBoleto.first().titleValue).isEqualTo("*****************")
    }
}
